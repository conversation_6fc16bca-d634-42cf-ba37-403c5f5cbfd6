apiVersion: apps/v1
kind: Deployment
metadata:
    name: shop-vue
    labels:
        app: shop-vue
spec:
    replicas: 1
    selector:
        matchLabels:
            app: shop-vue
    template:
        metadata:
            labels:
                app: shop-vue
        spec:
            containers:
                - name: shop-vue
                  image: registry.cn-shenzhen.aliyuncs.com/fulfillmen-shop/shop-vue:t-2025-03-11
                  ports:
                      - containerPort: 80
                  readinessProbe:
                      httpGet:
                          path: /health
                          port: 80
                      initialDelaySeconds: 10
                      periodSeconds: 5
                      timeoutSeconds: 2
                      successThreshold: 1
                      failureThreshold: 3
                  livenessProbe:
                      httpGet:
                          path: /health
                          port: 80
                      initialDelaySeconds: 30
                      periodSeconds: 30
                      timeoutSeconds: 5
                      successThreshold: 1
                      failureThreshold: 3
                  resources:
                      requests:
                          cpu: 100m
                          memory: 128Mi
                      limits:
                          cpu: 200m
                          memory: 256Mi
