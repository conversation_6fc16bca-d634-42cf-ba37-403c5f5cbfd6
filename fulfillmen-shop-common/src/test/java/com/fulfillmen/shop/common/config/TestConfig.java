/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.config;

import com.fulfillmen.shop.common.exception.handler.GlobalSaTokenExceptionHandler;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ResourceBundleMessageSource;

/**
 * 测试配置类 - 只加载异常处理器测试所需的最小配置
 *
 * <AUTHOR>
 * @date 2025/1/12
 */
@TestConfiguration
public class TestConfig {

    /**
     * 配置MessageSource用于国际化测试
     */
    @Bean
    @Primary
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();

        // 设置资源文件基础名称（不包含扩展名和语言后缀）
        messageSource.setBasenames("message", "openapi-message");

        // 设置默认编码
        messageSource.setDefaultEncoding("UTF-8");

        // 设置缓存时间（测试环境可以设置为0以便调试）
        messageSource.setCacheSeconds(0);

        // 当找不到消息时，不使用消息键作为默认值，而是抛出异常
        messageSource.setUseCodeAsDefaultMessage(false);

        // 设置回退到系统默认语言环境
        messageSource.setFallbackToSystemLocale(true);

        return messageSource;
    }

    /**
     * 配置全局SaToken异常处理器
     */
    @Bean
    public GlobalSaTokenExceptionHandler globalSaTokenExceptionHandler() {
        return new GlobalSaTokenExceptionHandler();
    }
}
