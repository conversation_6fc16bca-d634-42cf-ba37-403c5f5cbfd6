<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop-system</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>frontend-service</artifactId>
    <name>frontend-service</name>
    <description>
        ERP 前端的业务代码
    </description>
    <packaging>jar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-dao</artifactId>
            <!-- 依赖不需要不传递 -->
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-manager</artifactId>
            <!-- 依赖不需要不传递 -->
            <optional>true</optional>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>
</project>
