<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>fulfillmen-shop-system</artifactId>
    <name>Fulfillmen Shop - 系统模块 ${project.version}</name>
    <packaging>pom</packaging>
    <description>
        系统模块 - 分两个子模块：
        1. frontend 前端模块：采购系统的门户网站。
        2. admin 后台模块：采购系统的后台管理。
    </description>

    <modules>
        <module>fulfillmen-shop-frontend/frontend-service</module>
        <module>fulfillmen-shop-frontend/frontend-web</module>
    </modules>

    <dependencies>

        <!-- 公共配置和工具 -->
        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-common</artifactId>
        </dependency>

        <!-- 实体 -->
        <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>fulfillmen-shop-domain</artifactId>
        </dependency>

    </dependencies>

</project>
