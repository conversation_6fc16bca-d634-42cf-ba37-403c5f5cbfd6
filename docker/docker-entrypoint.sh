#!/bin/sh
set -e

# 显示当前环境变量用于调试
echo "API_BACKEND_URL = ${API_BACKEND_URL}"

# 确保API_BACKEND_URL以斜杠结尾
if [ "${API_BACKEND_URL}" != "" ] && [ "${API_BACKEND_URL: -1}" != "/" ]; then
    export API_BACKEND_URL="${API_BACKEND_URL}/"
    echo "已将API_BACKEND_URL修正为: ${API_BACKEND_URL}"
fi

# 替换环境变量
envsubst '${API_BACKEND_URL}' < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf
# 显示替换后的配置文件用于调试
echo "Nginx配置文件内容:"
cat /etc/nginx/conf.d/default.conf

# 执行CMD命令
exec "$@"