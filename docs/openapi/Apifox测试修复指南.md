# Apifox 测试修复指南

## 🚨 问题诊断

当您遇到以下错误时：
```
HandlerMethodValidationException: 400 BAD_REQUEST "Validation failure"
```

但同时看到签名验证成功的日志：
```
Signature validation passed for request: GET /openapi/v1/products
OpenAPI request validation passed - Account: 测试账户, Interface: GET /openapi/v1/products
```

这说明**签名验证成功**，但**参数验证失败**。

## ✅ 解决方案

### 1. 修正 language 参数

**❌ 错误的参数值：**
```
language: "cn"
```

**✅ 正确的参数值：**
```
language: "zh"  (中文)
或
language: "en"  (英文)
```

### 2. 在 Apifox 中修改

1. 打开您的 Apifox 接口测试
2. 找到 `language` 参数
3. 将值从 `cn` 修改为 `zh`
4. 重新发送请求

### 3. 验证修复

修改后，您应该看到成功的响应：
```json
{
  "records": [...],
  "total": 1000,
  "current": 1,
  "size": 20
}
```

## 📋 正确的测试参数示例

### 产品搜索接口测试

```
GET /openapi/v1/products

Query 参数：
- keyword: "手机"
- language: "zh"        ← 注意：使用 zh 而不是 cn
- page: "1"
- pageSize: "20"
- timestamp: [自动生成]
- nonce: [自动生成]
- sign: [自动生成]

Headers：
- x-fulfillmen-key: "testAccessKey"
```

### 产品详情接口测试

```
GET /openapi/v1/products/{productId}

Query 参数：
- language: "zh"        ← 注意：使用 zh 而不是 cn
- forceRefresh: "false"
- timestamp: [自动生成]
- nonce: [自动生成]
- sign: [自动生成]

Headers：
- x-fulfillmen-key: "testAccessKey"
```

## 🎯 参数验证规则

我们的 API 严格验证参数格式：

| 参数 | 验证规则 | 允许值 | 说明 |
|------|---------|--------|------|
| language | `^(zh\|en)?$` | `zh`, `en`, 或空 | 中文使用 zh，英文使用 en |
| page | 正整数 | `1`, `2`, `3`, ... | 页码从1开始 |
| pageSize | 正整数 | `1-100` | 每页大小限制 |

## 🔧 常见问题修复

### 问题1：language 参数错误
- ❌ `cn`, `chinese`, `中文`
- ✅ `zh`

### 问题2：缺少必需的签名参数
确保包含以下参数：
- `timestamp` (自动生成)
- `nonce` (自动生成)
- `sign` (自动生成)

### 问题3：AccessKey 错误
检查 Headers 中的：
- `x-fulfillmen-key: "testAccessKey"`

## 🎉 测试成功标志

当看到以下日志时，说明测试完全成功：
```
Signature validation passed for request: GET /openapi/v1/products
OpenAPI request validation passed - Account: 测试账户, Interface: GET /openapi/v1/products
```

并且收到正常的 JSON 响应数据。

## 📞 如需帮助

如果按照上述步骤仍有问题，请提供：
1. 完整的请求参数
2. 完整的错误日志
3. Apifox 的请求详情截图
