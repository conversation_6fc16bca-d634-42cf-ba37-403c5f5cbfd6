# 阿里巴巴Ocean客户端依赖配置说明

## 问题背景

本项目使用了阿里巴巴的Ocean客户端SDK（`ocean.client.java.biz.jar`），该jar包：
1. 不在公共Maven仓库中
2. 使用system scope配置在`fulfillmen-support-alibaba`模块中
3. system scope依赖无法传递到其他依赖模块

## 解决方案

### 本地开发环境

在首次克隆项目或遇到依赖问题时，运行以下命令：

```bash
# 在fulfillmen-shop项目根目录下
./install-alibaba-deps.sh
```

这个脚本会：
1. 检查jar文件是否存在
2. 将jar安装到本地Maven仓库
3. 解决依赖传递问题

### CI/CD环境

在CI构建脚本中添加以下步骤：

```yaml
# GitHub Actions 示例
- name: Install Alibaba Dependencies
  run: |
    chmod +x install-alibaba-deps.sh
    ./install-alibaba-deps.sh

- name: Build Project
  run: mvn clean compile
```

```bash
# 普通Shell脚本
#!/bin/bash
cd /path/to/fulfillmen-shop
chmod +x install-alibaba-deps.sh
./install-alibaba-deps.sh
mvn clean compile
```

## 依赖信息

- **GroupId**: `com.alibaba.platform.shared`
- **ArtifactId**: `ocean.client.java.basic`
- **Version**: `1.0.7`
- **源文件位置**: `../fulfillmen-support/fulfillmen-support-alibaba/src/main/libs/ocean.client.java.biz.jar`

## 故障排除

### 1. jar文件不存在
```
错误: 找不到jar文件 ../fulfillmen-support/fulfillmen-support-alibaba/src/main/libs/ocean.client.java.biz.jar
```

**解决方案**: 确保`fulfillmen-support`项目已正确克隆到与`fulfillmen-shop`同级的目录

### 2. 编译时仍然报错
```
Could not find artifact com.alibaba.platform.shared:ocean.client.java.basic:jar:1.0.7
```

**解决方案**:
1. 重新运行安装脚本：`./install-alibaba-deps.sh`
2. 清理Maven缓存：`mvn dependency:purge-local-repository`
3. 重新编译：`mvn clean compile`

### 3. CI环境权限问题

确保CI环境有执行脚本的权限：
```bash
chmod +x install-alibaba-deps.sh
```

## 项目结构要求

```
work/fulfillmen/
├── fulfillmen-shop/           # 主项目
│   ├── install-alibaba-deps.sh
│   └── pom.xml
└── fulfillmen-support/        # 支持模块
    └── fulfillmen-support-alibaba/
        └── src/main/libs/
            └── ocean.client.java.biz.jar
```

## 注意事项

1. 该jar包含有复杂的内部依赖关系
2. 不要将jar文件提交到Git仓库（已在.gitignore中排除）
3. 新团队成员需要先运行安装脚本再开始开发
4. 如果更新了jar版本，需要同步更新脚本中的版本号
