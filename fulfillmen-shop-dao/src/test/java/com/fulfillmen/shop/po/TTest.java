/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:52
 * @description: todo
 * @since 1.0.0
 */

/**
 * 测试 json
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(autoResultMap = true)
public class TTest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Integer id;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<AttrJson> json;
}
