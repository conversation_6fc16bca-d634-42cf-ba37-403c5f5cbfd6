<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.TenantDomainsMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.TenantDomains">
    <!--@mbg.generated-->
    <!--@Table tenant_domains-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="certificate_info" jdbcType="VARCHAR" property="certificateInfo" />
    <result column="is_primary" jdbcType="BOOLEAN" property="isPrimary" />
    <result column="verified" jdbcType="BOOLEAN" property="verified" />
    <result column="is_deleted" jdbcType="BIGINT" property="isDeleted" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, domain_name, certificate_info, is_primary, verified, is_deleted, revision, 
    gmt_created, gmt_modified
  </sql>
</mapper>