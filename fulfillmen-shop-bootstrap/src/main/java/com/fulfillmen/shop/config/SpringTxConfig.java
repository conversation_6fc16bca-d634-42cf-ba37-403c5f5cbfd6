package com.fulfillmen.shop.config;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * SpringTxConfig
 * <pre>
 *    编程式事务管理器
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/5
 */
@Configuration
@ConditionalOnBean(HikariDataSource.class)
public class SpringTxConfig {

    @Bean
    public TransactionTemplate nayaTransactionTemplate(PlatformTransactionManager transactionManager) {
        // 定义默认的 transactionDefinition 行为
        final DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        // 默认超时 20s
        transactionDefinition.setTimeout(20);
        // 手动事务
        transactionDefinition.setName("NayaManualTx");
        // 采用读已提交级别 暂时不开启，需要研究清楚
        // transactionDefinition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        return new TransactionTemplate(transactionManager, transactionDefinition);
    }

}
