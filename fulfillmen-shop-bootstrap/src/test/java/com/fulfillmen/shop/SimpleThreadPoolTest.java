/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 简单的线程池测试应用
 *
 * <AUTHOR>
 * @date 2025年07月17日22:50:00
 */
@SpringBootApplication
@Profile("dev")
public class SimpleThreadPoolTest implements CommandLineRunner {

    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "threadpool-test");
        SpringApplication.run(SimpleThreadPoolTest.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("=== 线程池配置测试 ===");

        if (threadPoolTaskExecutor != null) {
            System.out.println("✅ 线程池Bean存在: " + threadPoolTaskExecutor.getClass().getName());

            // 测试线程池执行
            threadPoolTaskExecutor.execute(() -> {
                System.out.println("✅ 线程池任务执行成功，线程名: " + Thread.currentThread().getName());
            });

            Thread.sleep(1000); // 等待任务完成
        } else {
            System.out.println("❌ 线程池Bean不存在");
        }

        System.out.println("=== 测试完成 ===");
    }
}
