/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.goods;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import com.fulfillmen.support.alibaba.enums.LanguageEnum;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 多语言商品店铺请求参数
 *
 * <AUTHOR>
 * @created 2025-01-17
 */
@Builder
public record GoodsSellerRequestRecord(
    /**
     * 关键词
     */
    @JsonProperty("keyword") String keyword,
    /**
     * 开始页码
     */
    @JsonProperty("beginPage") Integer beginPage,
    /**
     * 每页大小
     */
    @JsonProperty("pageSize") Integer pageSize,
    /**
     * 筛选条件
     */
    @JsonProperty("filter") String filter,
    /**
     * 排序条件
     */
    @JsonProperty("sort") String sort,
    /**
     * 外部用户ID
     */
    @JsonProperty("outMemberId") String outMemberId,
    /**
     * 批发价开始
     */
    @JsonProperty("priceStart") String priceStart,
    /**
     * 批发价结束
     */
    @JsonProperty("priceEnd") String priceEnd,
    /**
     * 类目ID
     */
    @JsonProperty("categoryId") Long categoryId,
    /**
     * 语言
     */
    @JsonProperty("country") String country,
    /**
     * 商家店铺id
     */
    @JsonProperty("sellerOpenId") String sellerOpenId
) implements BaseAlibabaRequestRecord {

    /**
     * 默认分页大小
     */
    private static final int DEFAULT_PAGE_SIZE = 20;
    /**
     * 默认页码
     */
    private static final int DEFAULT_PAGE_NO = 1;
    /**
     * 最大分页大小
     */
    private static final int MAX_PAGE_SIZE = 50;

    /**
     * 创建多语言商品店铺请求
     *
     * @param country      城市（必填）
     * @param sellerOpenId 商家店铺id（必填）
     * @return 多语言商品店铺请求
     */
    public static GoodsSellerRequestRecord of(LanguageEnum country, String sellerOpenId) {
        return of(null, DEFAULT_PAGE_NO, DEFAULT_PAGE_SIZE, null, null, null, null, null, null, country, sellerOpenId);
    }

    /**
     * 创建多语言商品店铺请求
     *
     * @param country      城市（必填）
     * @param sellerOpenId 商家店铺id（必填）
     * @param beginPage    开始页码（选填）
     * @param pageSize     每页条数（必填）
     * @return 多语言商品店铺请求
     */
    public static GoodsSellerRequestRecord of(LanguageEnum country,
        String sellerOpenId,
        Integer beginPage,
        Integer pageSize) {
        return of(null, beginPage, pageSize, null, null, null, null, null, null, country, sellerOpenId);
    }

    /**
     * 创建多语言商品店铺请求
     *
     * @param keyword      关键词（选填）
     * @param beginPage    分页页码（选填）
     * @param pageSize     每页条数（必填）
     * @param filter       筛选参数（选填）示例：shipInToday,ksCiphertext
     * @param sort         排序参数（选填）示例：{"price":"asc"}
     * @param outMemberId  外部用户ID（选填）
     * @param priceStart   批发价开始（选填）
     * @param priceEnd     批发价结束（选填）
     * @param categoryId   类目ID（选填）
     * @param country      城市（必填）
     * @param sellerOpenId 商家店铺id（必填）
     * @return 多语言商品店铺请求
     */
    public static GoodsSellerRequestRecord of(String keyword,
        Integer beginPage,
        Integer pageSize,
        String filter,
        String sort,
        String outMemberId,
        String priceStart,
        String priceEnd,
        Long categoryId,
        LanguageEnum country,
        String sellerOpenId) {
        // 分页大小最大不超过50，建议20效果最佳
        pageSize = Math.min(pageSize, MAX_PAGE_SIZE);
        return new GoodsSellerRequestRecord(keyword, beginPage, pageSize, filter, sort, outMemberId, priceStart, priceEnd, categoryId, country
            .getLanguage(), sellerOpenId);
    }

    @Override
    public void requireParams() {
        assertNotNull(country, "语言不能为空");
        assertNotBlank(sellerOpenId, "商家店铺id不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        if (keyword != null) {
            params.put("keyword", keyword);
        }
        params.put("beginPage", beginPage == null ? String.valueOf(DEFAULT_PAGE_NO) : beginPage.toString());
        params.put("pageSize", pageSize == null ? String.valueOf(DEFAULT_PAGE_SIZE) : pageSize.toString());
        if (filter != null) {
            params.put("filter", filter);
        }
        if (sort != null) {
            params.put("sort", sort);
        }
        if (outMemberId != null) {
            params.put("outMemberId", outMemberId);
        }
        if (priceStart != null) {
            params.put("priceStart", priceStart);
        }
        if (priceEnd != null) {
            params.put("priceEnd", priceEnd);
        }
        if (categoryId != null) {
            params.put("categoryId", categoryId.toString());
        }
        params.put("country", country);
        params.put("sellerOpenId", sellerOpenId);
        return params;
    }
}