/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api;

import com.fulfillmen.support.alibaba.api.response.feedback.AccountBusinessSaveResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.LogisticsOrderSyncResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.OrderRelationWriteResponse;
import com.fulfillmen.support.alibaba.api.response.feedback.OrderSyncResponse;
import org.springframework.http.MediaType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * 1688回传数据相关API
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@HttpExchange("/")
public interface FeedbackAPI {

    /**
     * 保存账号所属业务线
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 保存结果
     * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:account.business.save-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.FeedbackAPI.ACCOUNT_BUSINESS_SAVE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<AccountBusinessSaveResponse> saveAccountBusiness(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 国家站物流单回传
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 回传结果
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:trade.cross.logisticsOrderSync-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.FeedbackAPI.LOGISTICS_ORDER_SYNC, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<LogisticsOrderSyncResponse> syncLogisticsOrder(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 回传机构真实用户订单和1688订单的映射关系
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 回传结果
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:order.relation.write-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.FeedbackAPI.ORDER_RELATION_WRITE, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<OrderRelationWriteResponse> writeOrderRelation(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);

    /**
     * 下游销售订单同步
     *
     * @param appKey 应用key
     * @param params 请求参数
     * @return 同步结果
     * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.fenxiao.crossborder:trade.cross.orderSync-1">API文档</a>
     */
    @PostExchange(value = ApiPaths.FeedbackAPI.ORDER_SYNC, contentType = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    Mono<OrderSyncResponse> syncOrder(@PathVariable("APPKEY") String appKey,
        @RequestBody MultiValueMap<String, String> params);
}