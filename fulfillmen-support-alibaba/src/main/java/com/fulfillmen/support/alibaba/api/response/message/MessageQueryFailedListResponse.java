/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.message;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询式获取失败的消息列表响应
 *
 * <AUTHOR>
 * @created 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageQueryFailedListResponse extends BaseAlibabaResponse {

    /**
     * 分页数据
     */
    private PushMessagePage pushMessagePage;

    @Data
    public static class PushMessagePage {

        /**
         * 分页的消息数据列表
         */
        private List<PushMessage> datas;

        /**
         * 消息总数
         */
        private Integer totalCount;
    }

    @Data
    public static class PushMessage {

        /**
         * 消息唯一id
         */
        private Long msgId;

        /**
         * 消息类型
         */
        private String type;

        /**
         * 消息关联的用户memberId
         */
        private String userInfo;

        /**
         * 消息内容
         */
        private Map<String, Object> data;

        /**
         * 消息创建的时间戳，单位毫秒
         */
        private Long gmtBorn;
    }
}