/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 订单业务信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 */
@Data
public class TradeOrderBizInfo {

    /**
     * 是否采源宝订单
     */
    @JsonProperty("odsCyd")
    private Boolean odsCyd;

    /**
     * 诚e赊支付详情，只有使用诚e赊付款时返回
     */
    @JsonProperty("creditOrderDetail")
    private TradeCreditOrderDetail creditOrderDetail;

    /**
     * 预订单信息
     */
    @JsonProperty("preOrderInfo")
    private PreOrderInfo preOrderInfo;

    /**
     * 零售通订单信息
     */
    private TradeLstInfo lstOrderInfo;

    /**
     * 账期交易订单的到账时间
     */
    @JsonProperty("accountPeriodTime")
    private String accountPeriodTime;

    /**
     * 为true，表示下单时选择了诚e赊交易方式
     * <p>
     * 为true，表示下单时选择了诚e赊交易方式。注意不等同于“诚e赊支付”，支付时有可能是支付宝付款，具体支付方式查询tradeTerms.payWay
     * </p>
     */
    @JsonProperty("creditOrder")
    private Boolean creditOrder;

    /**
     * 是否dropshipping订单
     */
    @JsonProperty("dropshipping")
    private Boolean dropShipping;

    /**
     * ERP的用户ID
     *
     * <pre>
     * 示例：U001012121
     * </pre>
     */
    @JsonProperty("erpBuyerUserId")
    private String erpBuyerUserId;

    /**
     * ERP的订单编号
     *
     * <pre>
     * 示例：O123331
     * </pre>
     */
    @JsonProperty("erpOrderId")
    private String erpOrderId;

    /**
     * ERP组织ID
     *
     * <pre>
     * 示例：OG4331113
     * </pre>
     */
    @JsonProperty("erpBuyerOrgId")
    private String erpBuyerOrgId;

    /**
     * 是否加工定制订单
     *
     * <pre>
     * 示例：false
     * </pre>
     */
    @JsonProperty("isCz")
    private Boolean isCz;

    /**
     * 是否定制订单
     *
     * <pre>
     * 示例：false
     * </pre>
     */
    @JsonProperty("isDz")
    private Boolean isDz;

    /**
     * 是否定制订单
     *
     * <pre>
     * 示例：false
     * </pre>
     */
    @JsonProperty("dz")
    private Boolean dz;

    /**
     * 是否dropshipping订单，该类型订单不允许合并发货
     *
     * <pre>
     * 示例：true
     * </pre>
     */
    @JsonProperty("dropshipping")
    private Boolean dropshipping;

    /**
     * 运费险信息
     *
     * <pre>
     * givenByPlatform:平台赠送运费险
     * givenByMerchant:商家赠送运费险
     * 为空表示订单无运费险
     * 示例：givenByPlatform
     * </pre>
     */
    @JsonProperty("shippingInsurance")
    private String shippingInsurance;

    /**
     * 大店仓发订单
     *
     * <pre>
     * 示例：true
     * </pre>
     */
    @JsonProperty("hyperLinkCangFaOrder")
    private Boolean hyperLinkCangFaOrder;

    /**
     * 超链一阶段订单
     *
     * <pre>
     * 示例：false
     * </pre>
     */
    @JsonProperty("hyperLinkOrder")
    private Boolean hyperLinkOrder;

    /**
     * 超链大店二阶段订单的第二阶段订单
     *
     * <pre>
     * 示例：true
     * </pre>
     */
    @JsonProperty("hyperLinkSecondStepOrder")
    private Boolean hyperLinkSecondStepOrder;

    /**
     * 超链一阶段订单的发货模式
     *
     * <pre>
     * 0 仓发
     * 1 商发
     * 示例：0
     * </pre>
     */
    @JsonProperty("hyperLinkShipType")
    private String hyperLinkShipType;

    /**
     * 闪电仓订单
     *
     * <pre>
     * 示例：true
     * </pre>
     */
    @JsonProperty("lightningWarehouse")
    private Boolean lightningWarehouse;

    /**
     * ae上门揽订单
     *
     * <pre>
     * 示例：true
     * </pre>
     */
    @JsonProperty("aeDoorPickUp")
    private Boolean aeDoorPickUp;

    /**
     * 预订单信息
     */
    @Data
    public static class PreOrderInfo {

        /**
         * 创建预订单时传入的市场名
         */
        @JsonProperty("marketName")
        private String marketName;

        /**
         * 预订单是否为当前查询的通过当前查询的ERP创建
         */
        @JsonProperty("createPreOrderApp")
        private Boolean createPreOrderApp;
    }

    /**
     * 零售通订单信息
     */
    @Data
    public static class TradeLstInfo {

        /**
         * 零售通仓库类型。
         * <p>
         * customer：虚仓； cainiao：实仓
         * </p>
         */
        private String lstWarehouseType;

    }
}