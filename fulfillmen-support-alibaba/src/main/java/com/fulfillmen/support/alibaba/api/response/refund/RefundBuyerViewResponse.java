/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.refund;

import com.fulfillmen.support.alibaba.api.response.base.BaseAlibabaResponse;
import com.fulfillmen.support.alibaba.api.response.model.RefundBuyerViewResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询退款单详情(根据退款单ID)响应
 * <p>
 * 根据退款单ID查询退款单详细信息的响应结果，包含退款单状态、金额、物流等详细信息
 *
 * <AUTHOR>
 * @created 2025-01-13
 * @see RefundBuyerViewResult
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefundBuyerViewResponse extends BaseAlibabaResponse {

    /**
     * 退款单详情查询结果
     */
    private RefundBuyerViewResult result;
}