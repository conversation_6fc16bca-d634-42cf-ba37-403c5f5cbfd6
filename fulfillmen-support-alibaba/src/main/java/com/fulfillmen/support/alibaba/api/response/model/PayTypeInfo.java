/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.response.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付类型信息
 * <p>
 * 描述支付通道的编码和名称信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href="https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.payWay.query-1">API文档</a>
 */
@Data
@NoArgsConstructor
public class PayTypeInfo {

    /**
     * 支付通道编码
     * <p>
     * 可选值： - 1: 支付宝 - 3: 诚e赊 - 4: 公对公转账 - 7: 账期支付 - 15: 银行转账 - 16: 诚易宝 - 20: 跨境宝
     */
    private Long code;

    /**
     * 支付通道名称
     * <p>
     * 对应支付通道编码的中文名称
     */
    private String name;
}