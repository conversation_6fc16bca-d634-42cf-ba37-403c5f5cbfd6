/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.api.request.order;

import com.fulfillmen.support.alibaba.api.request.base.BaseAlibabaRequestRecord;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * 获取订单详情请求
 * <p>
 * 用于获取1688或国际站的订单详细信息
 *
 * <AUTHOR>
 * @created 2025-01-10
 * @see <a href= "https://open.1688.com/api/apidocdetail.htm?id=com.alibaba.trade:alibaba.trade.get.buyerView-1">API文档</a>
 */
@Builder
public record OrderDetailRequestRecord(
    /**
     * 站点信息，指定调用的API是属于国际站还是1688网站
     * <p>
     * 1688网站: 1688
     * 国际站: alibaba
     */
    String webSite,

    /**
     * 订单ID
     */
    Long orderId,

    /**
     * 是否需要返回买家详细地址信息和电话
     */
    Boolean needBuyerAddressAndPhone,

    /**
     * 是否需要返回备注信息
     */
    Boolean needMemoInfo,

    /**
     * 是否需要返回发票信息
     */
    Boolean needInvoiceInfo
) implements BaseAlibabaRequestRecord {

    /**
     * 创建 OrderDetailRequestRecord 实例
     *
     * @param webSite 站点信息
     * @param orderId 订单ID
     * @return OrderDetailRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static OrderDetailRequestRecord of(String webSite, Long orderId) {
        return new OrderDetailRequestRecord(webSite, orderId, null, null, null);
    }

    /**
     * 创建 OrderDetailRequestRecord 实例
     *
     * @param webSite                  站点信息
     * @param orderId                  订单ID
     * @param needBuyerAddressAndPhone 是否需要返回买家详细地址信息和电话
     * @param needMemoInfo             是否需要返回备注信息
     * @param needInvoiceInfo          是否需要返回发票信息
     * @return OrderDetailRequestRecord 实例
     * @throws IllegalArgumentException 如果参数无效
     */
    public static OrderDetailRequestRecord of(String webSite,
        Long orderId,
        Boolean needBuyerAddressAndPhone,
        Boolean needMemoInfo,
        Boolean needInvoiceInfo) {
        return new OrderDetailRequestRecord(webSite, orderId, needBuyerAddressAndPhone, needMemoInfo, needInvoiceInfo);
    }

    @Override
    public void requireParams() {
        assertNotBlank(webSite, "站点不能为空");
        assertNotNull(orderId, "订单ID不能为空");
    }

    @Override
    public Map<String, String> toParams() {
        Map<String, String> params = new HashMap<>();
        params.put("webSite", webSite);
        params.put("orderId", orderId.toString());
        if (needBuyerAddressAndPhone != null) {
            params.put("needBuyerAddressAndPhone", needBuyerAddressAndPhone.toString());
        }
        if (needMemoInfo != null) {
            params.put("needMemoInfo", needMemoInfo.toString());
        }
        if (needInvoiceInfo != null) {
            params.put("needInvoiceInfo", needInvoiceInfo.toString());
        }
        return params;
    }
}