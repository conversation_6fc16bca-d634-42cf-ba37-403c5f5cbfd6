package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * 订单退款枚举状态
 * 
 * <pre>
 * 订单的售中退款状态，等待卖家同意：waitselleragree ，待买家修改：waitbuyermodify，等待买家退货：waitbuyersend，等待卖家确认收货：waitsellerreceive，退款成功：refundsuccess，退款失败：refundclose
 * </pre>
 * 
 * <AUTHOR>
 * @date 2025/7/28 14:30
 * @description: 订单退款状态
 * @since 1.0.0
 */
@Getter
public enum OrderRefundStatusEnums {
    /**
     * 等待卖家同意
     */
    WAIT_SELLER_AGREE("waitselleragree", "等待卖家同意"),
    /**
     * 待买家修改
     */
    WAIT_BUYER_MODIFY("waitbuyermodify", "待买家修改"),
    /**
     * 等待买家退货
     */
    WAIT_BUYER_SEND("waitbuyersend", "等待买家退货"),
    /**
     * 等待卖家确认收货
     */
    WAIT_SELLER_RECEIVE("waitsellerreceive", "等待卖家确认收货"),
    /**
     * 退款成功
     */
    REFUND_SUCCESS("refundsuccess", "退款成功"),
    /**
     * 退款失败
     */
    REFUND_CLOSE("refundclose", "退款失败");
    ;

    private final String code;
    private final String desc;

    OrderRefundStatusEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
