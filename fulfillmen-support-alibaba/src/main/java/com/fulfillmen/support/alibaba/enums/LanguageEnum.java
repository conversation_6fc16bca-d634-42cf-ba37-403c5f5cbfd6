/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.enums;

import lombok.Getter;

/**
 * 阿里巴巴开放平台支持的语言枚举
 *
 * <AUTHOR>
 * @date 2024-12-31 09:13
 * @description: 定义阿里巴巴开放平台支持的多语言类型
 * @since 1.0.0
 */
@Getter
public enum LanguageEnum {

    ZH("zh", "中文"),
    EN("en", "英语"),
    JA("ja", "日语"),
    KO("ko", "韩语"),
    RU("ru", "俄罗斯"),
    VI("vi", "越南"),
    ES("es", "西班牙"),;

    private final String language;
    private final String desc;

    LanguageEnum(String language, String desc) {
        this.language = language;
        this.desc = desc;
    }

    /**
     * 根据语言代码获取枚举
     *
     * @param language 语言代码
     * @return 枚举
     */
    public static LanguageEnum fromLanguage(String language) {
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (languageEnum.getLanguage().equals(language)) {
                return languageEnum;
            }
        }
        return null;
    }

}
