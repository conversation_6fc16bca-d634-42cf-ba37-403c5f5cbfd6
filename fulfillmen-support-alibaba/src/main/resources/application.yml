## 阿里巴巴的相关配置
alibaba:
    # 支付宝相关配置
    alipay:
        # 支付宝应用ID,默认为沙箱环境的APPID
        app-id: ${ALIPAY_APP_ID:2016101300670001}
        # 支付宝网关地址,默认为正式环境地址
        gateway-url: ${ALIPAY_GATEWAY_URL:https://openapi.alipay.com/gateway.do}
    # 1688开放平台相关配置    
    open1688:
        # 1688应用的AppKey
        app-key: ${ALIBABA_1688_APP_KEY:8390330}
        # 1688应用的密钥
        secret-key: ${ALIBABA_1688_SECRET_KEY:3h27HVrZKU}
        # 1688开放平台网关地址
        gateway-url: ${ALIBABA_1688_GATEWAY_URL:https://gw.open.1688.com/openapi}
        # 1688访问令牌,用于调用API时的身份认证
        access-token: ${ALIBABA_1688_ACCESS_TOKEN:b49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2}

management:
    endpoints:
        web:
            exposure:
                # 暴露监控端点
                include: health,info,metrics,prometheus
    endpoint:
        health:
            # 显示详细的健康检查信息
            show-details: always
        metrics:
            enabled: true
        prometheus:
            enabled: true
    metrics:
        tags:
            # 应用名称标签
            application: ${spring.application.name}
        distribution:
            # 直方图配置，用于统计请求分布情况
            percentiles-histogram:
                http.server.requests: true
                # API调用持续时间分布
                alibaba.api.duration: true
                # API响应时间分布
                alibaba.api.response.time: true
                # API并发请求数分布
                alibaba.api.concurrent.requests: true
            # SLA目标配置，定义关键性能指标的目标值
            sla:
                # API调用持续时间的SLA目标
                alibaba.api.duration: 100ms,500ms,1s,5s
                # API响应时间的SLA目标
                alibaba.api.response.time: 100ms,500ms,1s,5s
            # 分位数配置，用于统计性能分布
            percentiles:
                # API响应时间的分位数统计点
                # 0.5 - 中位数
                # 0.75 - 75%的请求性能
                # 0.95 - 95%的请求性能
                # 0.99 - 99%的请求性能
                alibaba.api.response.time: 0.5,0.75,0.95,0.99
    prometheus:
        metrics:
            export:
                enabled: true