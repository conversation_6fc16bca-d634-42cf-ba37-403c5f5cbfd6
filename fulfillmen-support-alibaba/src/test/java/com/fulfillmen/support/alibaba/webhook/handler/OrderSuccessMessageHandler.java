/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.alibaba.webhook.handler;

import java.util.List;

import com.fulfillmen.support.alibaba.enums.CallbackMessageType;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.MessageHandler;
import com.fulfillmen.support.alibaba.webhook.MessageResult;
import com.fulfillmen.support.alibaba.webhook.data.OrderSuccessData;
import com.fulfillmen.starter.core.util.JacksonUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单成功消息处理器 (测试用)
 * 处理 ORDER_BUYER_VIEW_ORDER_SUCCESS 类型的消息
 * 
 * 注意：此类仅用于测试目的，不应在生产代码中使用
 *
 * <AUTHOR>
 * @date 2025/7/10
 * @since 1.0.0
 */
@Slf4j
public class OrderSuccessMessageHandler implements MessageHandler<OrderSuccessData> {

    @Override
    public boolean canHandle(CallbackMessageType messageType) {
        return OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS.equals(messageType);
    }

    @Override
    public MessageResult handle(MessageEvent<OrderSuccessData> event) {
        try {
            log.info("处理订单成功消息: msgId={}", event.getMsgId());

            // 转换数据
            OrderSuccessData data;
            if (event.getData() instanceof OrderSuccessData) {
                data = event.getData();
            } else {
                // 从rawData转换
                data = JacksonUtil.convertToBean(event.getRawData(), OrderSuccessData.class);
            }

            log.info("订单成功消息处理完成: orderId={}, buyerMemberId={}, status={}",
                data.getOrderId(), data.getBuyerMemberId(), data.getCurrentStatus());

            return MessageResult.success(event.getMsgId(), data);

        } catch (Exception e) {
            log.error("处理订单成功消息异常: msgId={}", event.getMsgId(), e);
            return MessageResult.error(event.getMsgId(), e.getMessage());
        }
    }

    @Override
    public int getPriority() {
        return 100;
    }

    @Override
    public List<CallbackMessageType> getSupportedTypes() {
        return List.of(OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS);
    }
}
