# 阿里巴巴订单详情查看API（买家视角）

## API概述

**API名称：** 订单详情查看(买家视角)  
**API标识：** com.alibaba.trade:alibaba.trade.get.buyerView-1  
**版本：** 1  
**请求方式：** POST  

## API描述

获取单个交易明细信息，仅限买家调用。该API需要向阿里巴巴开放平台申请权限才能使用。

Get a single transaction detail, only for users to call.

## 请求URL

```
POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/${APPKEY}
```

## 所属解决方案

该API属于以下解决方案：
- 联盟业务解决方案
- 档口经营解决方案（买家版）
- 采购解决方案（服务商版）
- 采购解决方案（买家自用版）
- 分销工具解决方案
- ERP分销解决方案（分销商侧）
- 代发解决方案（服务商版）
- 1688分销严选采购解决方案（服务商版）
- 1688分销严选采购解决方案（分销买家版）
- 代发解决方案（分销买家版）
- 新版电商ERP解决方案
- 1688国家服务站解决方案
- 跨境综合解决方案（自用）
- 跨境ERP/独立站SaaS数字化解决方案
- 跨境大客户寻源通解决方案
- 即时零售场景采购解决方案
- 跨境分销解决方案

## 系统级输入参数

| 参数名 | 类型 | 是否必须 | 描述 | 文档链接 |
|--------|------|----------|------|----------|
| _aop_timestamp | String | 否 | 请求时间戳 | [文档地址](https://open.1688.com/doc/apiInvoke.htm) |
| _aop_signature | String | 是 | 请求签名 | [文档地址](https://open.1688.com/doc/signature.htm) |
| access_token | String | 是 | 用户授权令牌 | [文档地址](https://open.1688.com/doc/apiAuth.htm) |

## 应用级输入参数

| 参数名 | 类型 | 是否必须 | 描述 | 示例值 |
|--------|------|----------|------|--------|
| webSite | String | 是 | 站点信息，指定调用的API是属于国际站（alibaba）还是1688网站（1688） | 1688 |
| orderId | Long | 是 | 交易的订单id | 123456 |
| includeFields | String | 否 | 查询结果中包含的域，GuaranteesTerms：保障条款，NativeLogistics：物流信息，RateDetail：评价详情，OrderInvoice：发票信息。默认返回GuaranteesTerms、NativeLogistics、OrderInvoice。 | GuaranteesTerms,NativeLogistics,RateDetail,OrderInvoice |
| attributeKeys | String[] | 否 | 垂直表中的attributeKeys | [] |
| outOrderId | String | 否 | 外部订单id，控制幂等 | 1556246 |

## 返回结果

| 参数名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| result | alibaba.openplatform.trade.model.TradeInfo | 订单详情信息 | {} |
| errorCode | String | 错误代码 | |
| errorMessage | String | 错误描述 | |
| success | String | 是否成功 | true |

## TradeInfo详细结构

| 字段名                  | 类型                                                                         | 描述                  |
|----------------------|----------------------------------------------------------------------------|---------------------|
| baseInfo             | alibaba.openplatform.trade.model.OrderBaseInfo                             | 订单基础信息              |
| orderBizInfo         | alibaba.order.bizInfo                                                      | 订单业务信息              |
| tradeTerms           | alibaba.openplatform.trade.model.TradeTermsInfo[]                          | 交易条款                |
| tradeProductItems    | alibaba.openplatform.trade.model.ProductItemInfo[]                         | 商品条目信息              |
| nativeLogistics      | alibaba.openplatform.trade.model.NativeLogisticsInfo                       | 国内物流                |
| orderInvoiceInfo     | alibaba.invoice.OrderInvoiceModel                                          | 发票信息                |
| guaranteesTerms      | alibaba.openplatform.trade.model.GuaranteeTermsInfo                        | 保障条款                |
| orderRateInfo        | alibaba.trade.OrderRateInfo                                                | 订单评价信息              |
| overseasExtraAddress | alibaba.trade.OverseasExtraAddress                                         | 跨境地址扩展信息            |
| customs              | alibaba.trade.Customs                                                      | 跨境报关信息              |
| quoteList            | alibaba.orderDetail.caigouQuoteInfo[]                                      | 采购单详情列表，为大企业采购订单独有域 |
| extAttributes        | alibaba.openplatform.trade.KeyValuePair[]                                  | 订单扩展属性              |
| fromEncryptOrder     | Boolean                                                                    | 是否下游脱敏信息创建的订单       |
| encryptOutOrderInfo  | alibaba.trade.get.sellerView.tradeinfo.EncryptOutOrderInfo                 | 外部订单信息              |
| overseaLogisticsInfo | com.alibaba.ocean.openplatform.biz.trade.common.model.OverseaLogisticsInfo | 海外物流信息              |

## 详细字段说明

### baseInfo - 订单基础信息字段

| 字段名 | 数据类型 | 描述 | 示例值 |
|--------|----------|------|--------|
| allDeliveredTime | java.util.Date | 完全发货时间 | 20180614101942000+0800 |
| sellerCreditLevel | java.lang.String | 卖家诚信等级 | L1 |
| payTime | java.util.Date | 付款时间，如果有多次付款，这里返回的是首次付款时间 | 20180614101942000+0800 |
| discount | Long | 折扣信息，单位分 | 11 |
| alipayTradeId | java.lang.String | 外部支付交易Id | 123123121111 |
| sumProductPayment | java.math.BigDecimal | 产品总金额(该订单产品明细表中的产品金额的和)，单位元 | 1212 |
| buyerFeedback | String | 买家留言，不超过500字 | 留言 |
| flowTemplateCode | String | 4.0交易流程模板code | flow |
| sellerOrder | java.lang.Boolean | 是否自主订单（邀约订单） | false |
| buyerLoginId | java.lang.String | 买家loginId，旺旺Id | alitestforusv01 |
| modifyTime | java.util.Date | 修改时间 | 20180614101942000+0800 |
| subBuyerLoginId | String | 买家子账号 | alitestforusv02:temp |
| id | java.lang.Long | 交易id | 1231231231111 |
| closeReason | java.lang.String | 关闭原因。buyerCancel:买家取消订单，sellerGoodsLack:卖家库存不足，other:其它 | buyerCancel |
| sellerAlipayId | java.lang.String | 卖家支付宝id | 12312311111 |
| completeTime | java.util.Date | 完成时间 | 20180614101942000+0800 |
| sellerLoginId | java.lang.String | 卖家loginId，旺旺Id | alitestforusv02 |
| buyerID | java.lang.String | 买家主账号id | 1234531 |
| closeOperateType | String | 关闭订单操作类型。CLOSE_TRADE_BY_SELLER:卖家关闭交易,CLOSE_TRADE_BY_BOPS:BOPS后台关闭交易,CLOSE_TRADE_BY_SYSTEM:系统（超时）关闭交易,CLOSE_TRADE_BY_BUYER:买家关闭交易,CLOSE_TRADE_BY_CREADIT:诚信保障投诉关闭 | CLOSE_TRADE_BY_SELLER |
| totalAmount | java.math.BigDecimal | 应付款总金额，totalAmount = ∑itemAmount + shippingFee，单位为元 | 1000 |
| sellerID | java.lang.String | 卖家主账号id | 123123123123 |
| shippingFee | java.math.BigDecimal | 运费，单位为元 | 1 |
| buyerUserId | java.lang.Long | 买家数字id | 12314144 |
| buyerMemo | java.lang.String | 买家备忘信息 | 备忘 |
| refund | java.math.BigDecimal | 退款金额，单位为元 | 1 |
| status | java.lang.String | 交易状态，waitbuyerpay:等待买家付款;waitsellersend:等待卖家发货;waitbuyerreceive:等待买家收货;confirm_goods:已收货;success:交易成功;cancel:交易取消;terminated:交易终止;未枚举:其他状态 | waitbuyerpay |
| refundPayment | Long | 退款金额 | 1 |
| couponFee | java.math.BigDecimal | 红包金额，实付金额（totalAmount）已经计算过红包金额 | 7.5 |
| buyerRemarkIcon | String | 买家备忘标志 | 1 |
| refundStatus | String | 订单的售中退款状态，等待卖家同意：waitselleragree ，待买家修改：waitbuyermodify，等待买家退货：waitbuyersend，等待卖家确认收货：waitsellerreceive，退款成功：refundsuccess，退款失败：refundclose | refundclose |
| remark | java.lang.String | 备注，1688指下单时的备注 | 备注 |
| preOrderId | java.lang.Long | 预订单ID | 123123 |
| confirmedTime | java.util.Date | 确认时间 | 20180614101942000+0800 |
| closeRemark | String | 关闭订单备注 | 备注 |
| createTime | java.util.Date | 创建时间 | 20180614101942000+0800 |
| businessType | String | 业务类型 | cn |
| tradeType | String | 交易类型 | 50060 |
| idOfStr | String | 交易ID字符串 | 58218860983545941 |
| stepPayAll | Boolean | 是否分阶段付款 | false |
| buyerContact | alibaba.trade.tradeContact | 买家联系人信息 | {} |
| sellerContact | alibaba.trade.tradeSellerContact | 卖家联系人信息 | {} |
| receiverInfo | alibaba.trade.orderReceiverInfo | 收件人信息 | {} |

### tradeProductItems - 商品条目信息字段

| 字段名 | 数据类型 | 描述 | 示例值 |
|--------|----------|------|--------|
| itemAmount | java.math.BigDecimal | 商品金额，单位元 | 0.3 |
| name | String | 商品名称 | 测试扫码购富光勿拍2L*6件 |
| price | java.math.BigDecimal | 商品单价，单位元 | 0.3 |
| productID | Long | 商品ID | ************ |
| productImgUrl | String[] | 商品图片URL列表 | ["http://..."] |
| productSnapshotUrl | String | 商品快照URL | https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=128403042259997715 |
| quantity | Integer | 购买数量 | 1 |
| refund | java.math.BigDecimal | 退款金额，单位元 | 0 |
| skuID | Long | SKU ID | ************* |
| status | String | 商品状态 | waitsellersend |
| subItemID | Long | 子订单ID | 128403042259997710 |
| type | String | 商品类型 | common |
| unit | String | 商品单位 | 箱 |
| guaranteesTerms | Object[] | 保障条款 | [] |
| skuInfos | Object[] | SKU信息 | [{"name":"颜色","value":"白色"}] |
| entryDiscount | java.math.BigDecimal | 条目折扣 | 0 |
| specId | String | 规格ID | 28b952ec96c8b5c3ab0affc1b74923f0 |
| quantityFactor | Integer | 数量因子 | 1 |
| statusStr | String | 状态描述 | 等待卖家发货 |
| refundStatus | String | 退款状态 | WAIT_SELLER_AGREE |
| logisticsStatus | Integer | 物流状态 | 1 |
| gmtCreate | String | 创建时间 | *****************+0800 |
| gmtModified | String | 修改时间 | *****************+0800 |
| gmtPayExpireTime | String | 支付过期时间 | 2018-02-07 15:27:58 |
| refundId | String | 退款ID | ****************** |
| subItemIDString | String | 子订单ID字符串 | 128403042259997715 |

### nativeLogistics - 国内物流信息字段

| 字段名 | 数据类型 | 描述 | 示例值 |
|--------|----------|------|--------|
| address | String | 收货地址 | 杭州市滨江区网商路699号 |
| area | String | 区域 | 滨江区 |
| areaCode | String | 区域代码 | 330108 |
| city | String | 城市 | 杭州市 |
| contactPerson | String | 联系人 | 童恩杰 |
| mobile | String | 手机号 | 13666836263 |
| province | String | 省份 | 浙江省 |
| zip | String | 邮编 | 312000 |
| townCode | String | 街道代码 | ********* |
| town | String | 街道 | 长河街道 |
| logisticsItems | Object[] | 物流条目列表 | [{}] |

### tradeTerms - 交易条款信息字段

| 字段名 | 数据类型 | 描述 | 示例值 |
|--------|----------|------|--------|
| payStatus | String | 支付状态 | 2 |
| payTime | String | 支付时间 | 20170913231727000-0700 |
| payWay | String | 支付方式 | 1 |
| phasAmount | java.math.BigDecimal | 阶段金额 | 6.15 |
| phase | Long | 阶段 | 655062941545941 |

### orderRateInfo - 订单评价信息字段

| 字段名 | 数据类型 | 描述 | 示例值 |
|--------|----------|------|--------|
| buyerRateStatus | Integer | 买家评价状态 | 5 |
| sellerRateStatus | Integer | 卖家评价状态 | 5 |

## 请求示例

```bash
curl -X POST \
  'https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/${APPKEY}' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'webSite=1688&orderId=123456&access_token=${ACCESS_TOKEN}&_aop_signature=${SIGNATURE}'
```

## 响应示例

```json
{
  "result": {
    "baseInfo": {
      "allDeliveredTime": "20170913231916000-0700",
      "businessType": "cn",
      "buyerID": "b2b-2248544159",
      "createTime": "20170913231708000-0700",
      "id": 58218860983545944,
      "modifyTime": "20170913234725000-0700",
      "payTime": "20170913231727000-0700",
      "refund": 0,
      "sellerID": "b2b-2248564064",
      "shippingFee": 6,
      "status": "waitbuyerreceive",
      "totalAmount": 6.15,
      "discount": 0,
      "buyerContact": {
        "phone": "86-0571-81895955",
        "imInPlatform": "b测试账号002",
        "name": "乔的石",
        "mobile": "***********",
        "companyName": "阿里巴巴网络科技有限公司"
      },
      "sellerContact": {
        "phone": "86-0571-********",
        "email": "<EMAIL>",
        "imInPlatform": "b测试账号110",
        "name": "孟舒",
        "mobile": "***********",
        "companyName": "阿里巴巴网络有限公司"
      },
      "tradeType": "50060",
      "refundStatus": "waitselleragree",
      "refundPayment": 0,
      "idOfStr": "58218860983545941",
      "alipayTradeId": "2017091421001008480237437679",
      "receiverInfo": {
        "toFullName": "童恩杰",
        "toDivisionCode": "330108",
        "toPost": "312000",
        "toTownCode": "*********",
        "toArea": "浙江省 杭州市 滨江区 长河街道"
      },
      "buyerLoginId": "b测试账号002",
      "sellerLoginId": "b测试账号110",
      "buyerUserId": 2248544159,
      "sellerUserId": 2248564064,
      "buyerAlipayId": "2088611489970483",
      "sellerAlipayId": "2088611383470360",
      "sumProductPayment": 0.3,
      "stepPayAll": false
    },
    "nativeLogistics": {
      "address": "杭州市滨江区网商路699号",
      "area": "滨江区",
      "areaCode": "330108",
      "city": "杭州市",
      "contactPerson": "童恩杰",
      "mobile": "13666836263",
      "province": "浙江省",
      "zip": "312000",
      "logisticsItems": [{
        "deliveredTime": "20170913231917000-0700",
        "logisticsCode": "BX107450035961376",
        "type": "2",
        "id": 107450035961376,
        "status": "alreadysend",
        "gmtModified": "20170913231916000-0700",
        "gmtCreate": "20170913231916000-0700",
        "fromPhone": "86-0571-********",
        "fromMobile": "***********",
        "logisticsBillNo": "不需要物流",
        "subItemIds": "*****************,*****************,*****************"
      }],
      "townCode": "*********",
      "town": "长河街道"
    },
    "tradeProductItems": [{
      "itemAmount": 0.3,
      "name": "测试扫码购富光勿拍2L*6件",
      "price": 0.3,
      "productID": ************,
      "productImgUrl": [
        "http://cbu01.alicdn.com/img/ibank/2017/757/125/**********.80x80.jpg",
        "http://cbu01.alicdn.com/img/ibank/2017/757/125/**********.jpg"
      ],
      "productSnapshotUrl": "https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=128403042259997715",
      "quantity": 1,
      "refund": 0,
      "skuID": *************,
      "status": "waitsellersend",
      "subItemID": 128403042259997710,
      "type": "common",
      "unit": "箱",
      "guaranteesTerms": [],
      "skuInfos": [{
        "name": "颜色",
        "value": "白色"
      }, {
        "name": "容量",
        "value": "1.1L-2L"
      }],
      "entryDiscount": 0,
      "specId": "28b952ec96c8b5c3ab0affc1b74923f0",
      "quantityFactor": 1,
      "statusStr": "等待卖家发货",
      "refundStatus": "WAIT_SELLER_AGREE",
      "logisticsStatus": 1,
      "gmtCreate": "*****************+0800",
      "gmtModified": "*****************+0800",
      "gmtPayExpireTime": "2018-02-07 15:27:58",
      "refundId": "******************",
      "subItemIDString": "128403042259997715"
    }],
    "tradeTerms": [{
      "payStatus": "2",
      "payTime": "20170913231727000-0700",
      "payWay": "1",
      "phasAmount": 6.15,
      "phase": 655062941545941
    }],
    "extAttributes": [],
    "orderRateInfo": {
      "buyerRateStatus": 5,
      "sellerRateStatus": 5
    }
  },
  "success": "true"
}
```

## 错误码说明

当API调用失败时，会返回相应的错误码和错误信息：

- `errorCode`: 错误代码
- `errorMessage`: 错误描述
- `success`: 调用是否成功

## 注意事项

1. 该API仅限买家调用
2. 需要向阿里巴巴开放平台申请相应权限
3. 需要正确的用户授权令牌
4. 请求签名必须正确
5. 订单ID必须是有效的订单ID

## 字段说明

### 订单状态说明

- `waitbuyerreceive`: 等待买家收货
- `waitsellersend`: 等待卖家发货
- `success`: 交易成功

### 支付状态说明

- `payStatus`: 支付状态（2表示已支付）
- `payWay`: 支付方式（1表示支付宝）

### 物流状态说明

- `alreadysend`: 已发货
- `logisticsStatus`: 物流状态（1-待发货，2-已发货）

### 退款状态说明

- `waitselleragree`: 等待卖家同意退款
- `WAIT_SELLER_AGREE`: 等待卖家同意

## 相关文档

- [API调用说明](https://open.1688.com/doc/apiInvoke.htm)
- [签名算法](https://open.1688.com/doc/signature.htm)
- [用户授权](https://open.1688.com/doc/apiAuth.htm)
