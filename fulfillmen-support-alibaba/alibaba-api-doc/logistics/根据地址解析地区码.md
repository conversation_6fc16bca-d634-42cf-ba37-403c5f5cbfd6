# 根据地址解析地区码

## 接口说明

根据地址信息，解析地区码。

## 请求URL

- POST `https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.addresscode.parse/${APPKEY}`

## 系统级输入参数

| 参数名            | 类型     | 是否必须 | 描述     | 示例值 |
|----------------|--------|------|--------|-----|
| _aop_timestamp | String | 否    | 请求时间戳  | -   |
| _aop_signature | String | 是    | 请求签名   | -   |
| access_token   | String | 是    | 用户授权令牌 | -   |

## 应用级输入参数

| 参数名         | 类型     | 是否必须 | 描述   | 示例值                |
|-------------|--------|------|------|--------------------|
| addressInfo | String | 是    | 地址信息 | 浙江省 杭州市 滨江区网商路699号 |

## 返回结果

| 参数名          | 类型                           | 描述       | 示例值 |
|--------------|------------------------------|----------|-----|
| result       | alibaba.trade.ReceiveAddress | 解析后的收货地址 | {}  |
| errorCode    | String                       | 错误码      | -   |
| errorMessage | String                       | 错误信息     | -   |

### ReceiveAddress 对象

| 参数名             | 类型               | 描述                     | 示例值          |
|-----------------|------------------|------------------------|--------------|
| address         | java.lang.String | 街道地址，不包括省市区            | 网商路699号      |
| addressCode     | java.lang.String | 地址区域编码                 | 330108       |
| addressCodeText | java.lang.String | 地址区域编码对应的文本（包括国家、省、城市） | 浙江省 杭州市 滨江区  |
| addressId       | java.lang.Long   | addressId              | 322683081    |
| bizType         | java.lang.String | 记录收货地址的业务类型            | 无须关注         |
| isDefault       | boolean          | 是否为默认                  | false        |
| fullName        | java.lang.String | 收货人姓名                  | 张三           |
| latest          | boolean          | 是否是最后选择的收货地址           | false        |
| mobile          | java.lang.String | 手机号                    | 18012345678  |
| phone           | java.lang.String | 电话                     | 0517-8888888 |
| postCode        | java.lang.String | 邮编                     | 310051       |

## 请求示例

```json
{
    "addressInfo": "浙江省 杭州市 滨江区网商路699号"
}
```

## 返回示例

```json
{
    "result": {
        "address": "网商路699号",
        "addressCode": "330108",
        "isDefault": false,
        "latest": false,
        "postCode": "310051"
    }
}
```
