# 商品中国国内运费预估

## 接口名称

com.alibaba.fenxiao.crossborder:product.freight.estimate-1

## 接口说明

根据商品ID、中国国内收货地址的省市区编码，预估商品的运费。

## 请求 URL

POST https://gw.open.1688.com/openapi/param2/1/com.alibaba.fenxiao.crossborder/product.freight.estimate/${APPKEY}

## 系统参数

| 名称             | 类型     | 是否必须 | 描述     | 文档        |
|----------------|--------|------|--------|-----------|
| _aop_timestamp | String | 否    | 请求时间戳  | [文档地址](#) |
| _aop_signature | String | 是    | 请求签名   | [文档地址](#) |
| access_token   | String | 是    | 用户授权令牌 | [文档地址](#) |

## 应用级入参

### productFreightQueryParamsNew

| 名称                    | 类型                         | 是否必须 | 描述     | 示例值        |
|-----------------------|----------------------------|------|--------|------------|
| offerId               | Long                       | 是    | 商品ID   | 111111111  |
| toProvinceCode        | String                     | 是    | 中国省份编码 | 如浙江省330000 |
| toCityCode            | String                     | 是    | 中国城市编码 | 如杭州市330100 |
| toCountryCode         | String                     | 是    | 中国地区编码 | 如浙江区330108 |
| totalNum              | Long                       | 是    | 购买件数   | 3          |
| logisticsSkuNumModels | List<LogisticsSkuNumModel> | 是    | sku件数  | 1          |

### logisticsSkuNumModels

| 名称     | 类型     | 是否必须 | 描述    | 示例值   |
|--------|--------|------|-------|-------|
| skuId  | String | 是    | skuId | 12345 |
| number | Long   | 是    | 数量    | 1     |

## 返回结果

### result

| 名称 | 类型 | 描述 | 示例值 |
| result | ResultModel | 内部结果 | 如下 |

### ResultModel

| 名称      | 类型                  | 描述   | 示例值   |
|---------|---------------------|------|-------|
| success | boolean             | 结果   | true  |
| code    | String              | 错误码  | S0000 |
| message | String              | 错误描述 | 成功    |
| result  | ProductFreightModel | 内部结果 | 如下    |

### ProductFreightModel

| 名称                          | 类型                               | 描述                                                 | 示例值                         |
|-----------------------------|----------------------------------|----------------------------------------------------|-----------------------------|
| offerId                     | Long                             | 商品ID                                               | 111111111                   |
| freight                     | String                           | 预估总运费                                              | 12.3                        |
| templateId                  | Long                             | 商家运费模板ID                                           | 2324342                     |
| singleProductWeight         | Double                           | 单商品重量，单位千克                                         | 0.15                        |
| singleProductWidth          | Double                           | 单商品宽度，单位厘米                                         | 10                          |
| singleProductHeight         | Double                           | 单商品高度，单位厘米                                         | 10                          |
| singleProductLength         | Double                           | 单商品长度，单位厘米                                         | 10                          |
| templateType                | Integer                          | 商家运费模板类型，1卖家承担运费，2用户自定义模板，3用户自定义复方模板               | 2                           |
| templateName                | String                           | 商家运费模板名称                                           | 测试                          |
| subTemplateType             | Integer                          | 商家运费子模板类型，0快递，1货运，2货到付款，3复方                        | 0                           |
| subTemplateName             | String                           | 商家运费子模板名称                                          | 测试                          |
| firstFee                    | String                           | 首重/件费用，单位元                                         | 2.9                         |
| firstUnit                   | String                           | 首重/件单位                                             | 如1代表【1件或者首重1千克是firstFee元】   |
| nextFee                     | String                           | 续重/件费用，单位元                                         | 2                           |
| nextUnit                    | String                           | 续重/件单位                                             | 如1代表【之后的1件或者续重1千克是nextFee元】 |
| discount                    | String                           | 运费折扣                                               | 空或者1，代表无折扣                  |
| chargeType                  | String                           | 计费类型，0重量，1件数，2件积                                   | 0                           |
| freePostage                 | Boolean                          | 是否包邮，true-包邮 false-不包邮，不包邮时freight读取               | true                        |
| productFreightSkuInfoModels | List<ProductFreightSkuInfoModel> | sku包装信息                                            | 1                           |
| sizeValueType               | Integer                          | 件量尺寸值类型，1-为外层取值，2-为从productFreightSkuInfoModels中取值 | 2                           |

### ProductFreightSkuInfoModel

| 名称              | 类型     | 描述    | 示例值      |
|-----------------|--------|-------|----------|
| skuId           | String | skuId | 12345678 |
| singleSkuWeight | Double | 重量    | 1        |
| singleSkuWidth  | Double | 宽     | 1        |
| singleSkuHeight | Double | 高     | 1        |
| singleSkuLength | Double | 长     | 1        |