# 图片翻译

## 接口说明

图片翻译接口用于翻译图片中的文本内容，支持多种语言转换，确保翻译内容与图片内容匹配。

## 接口信息

- **接口路径**: `/param2/1/com.alibaba.fenxiao.crossborder/image.elements.translate/${APPKEY}`
- **请求方式**: POST
- **接口版本**: 1.0
- **API名称**: com.alibaba.fenxiao.crossborder.image.elements.translate-1

## 系统级参数

| 参数名            | 类型     | 必填 | 描述     | 示例值 |
|----------------|--------|----|--------|-----|
| _aop_timestamp | String | 否  | 请求时间戳  | -   |
| _aop_signature | String | 是  | 请求签名   | -   |
| access_token   | String | 否  | 用户授权令牌 | -   |

## 应用级输入参数

| 参数名                    | 类型     | 必填 | 描述               | 示例值       |
|------------------------|--------|----|------------------|-----------|
| imageUrl               | String | 是  | 源图片URL，大小不超过10MB | http://xx |
| originalLanguage       | String | 是  | 源语言代码            | zh        |
| targetLanguage         | String | 是  | 目标语言代码           | en        |
| isIncludingProductArea | String | 否  | 是否翻译商品主体上的文字     | true      |
| useImageEditor         | String | 否  | 是否进行二次编辑         | false     |

## 返回结果

| 参数名     | 类型                   | 描述      | 示例值   |
|---------|----------------------|---------|-------|
| success | String               | 是否成功    | true  |
| code    | String               | -       | S0000 |
| message | String               | 成功/失败信息 | -     |
| result  | TranslatedImageModel | 翻译结果    | {}    |

### TranslatedImageModel 对象

| 参数名                      | 类型     | 描述 | 示例值 |
|--------------------------|--------|----|-----|
| originalImageUrl         | String | -  | -   |
| originalLanguage         | String | -  | -   |
| translatedImagePixelData | String | -  | -   |
| translatedImageUrl       | String | -  | -   |
| translatedLanguage       | String | -  | -   |

## 产品简介

图片翻译产品专为电商图片定制，支持18种语向，精确处理图片内的复杂文本布局，并确保翻译内容能够与图片内容匹配，帮助电商平台和开发者轻松实现图像内容的多语言转换与呈现。

## 请求示例

```json
{
    "imageUrl": "http://xx",
    "originalLanguage": "zh",
    "targetLanguage": "en",
    "isIncludingProductArea": "true",
    "useImageEditor": "false",
    "appKey": ""
}
```

## 返回示例

```json
{
    "success": "true",
    "code": "S0000",
    "message": "成功",
    "result": {
        "originalImageUrl": "http://xx",
        "originalLanguage": "zh",
        "translatedImagePixelData": "base64encodeddata",
        "translatedImageUrl": "http://translatedxx",
        "translatedLanguage": "en"
    }
}
```
