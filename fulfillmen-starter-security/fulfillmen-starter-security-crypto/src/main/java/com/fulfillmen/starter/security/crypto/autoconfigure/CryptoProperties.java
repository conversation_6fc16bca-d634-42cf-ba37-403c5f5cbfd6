/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.security.crypto.autoconfigure;

import com.fulfillmen.starter.core.constant.PropertiesConstants;
import com.fulfillmen.starter.security.crypto.enums.CryptoTypeEnum;
import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.io.Resource;

/**
 * 加/解密配置属性
 *
 * <AUTHOR>
 * @date 2025/2/5 10:57
 */
@Data
@ConfigurationProperties(PropertiesConstants.SECURITY_CRYPTO)
public class CryptoProperties {

    /**
     * 是否启用加/解密配置
     */
    private boolean enabled = true;

    /**
     * 对称加密算法密钥
     */
    private String password;

    /**
     * 非对称类型 如果是 文件则从文件读取
     */
    private CryptoTypeEnum asymmetricType;

    /**
     * 非对称加密算法公钥
     */
    private String publicKey;

    /**
     * 非对称加密算法私钥
     */
    private String privateKey;

    /**
     * 非对称加密算法公钥
     */
    private Resource publicKeyFile;

    /**
     * 非对称加密算法私钥
     */
    private Resource privateKeyFile;

    @PostConstruct
    public void init() throws IOException {
        if (asymmetricType != null && asymmetricType.equals(CryptoTypeEnum.FILE)) {
            if (privateKeyFile != null) {
                this.privateKey = formatKeyContent(privateKeyFile.getInputStream()
                    .readAllBytes(), "-----BEGIN ENCRYPTED PRIVATE KEY-----", "-----END ENCRYPTED PRIVATE KEY-----");
            }

            if (publicKeyFile != null) {
                this.publicKey = formatKeyContent(publicKeyFile.getInputStream()
                    .readAllBytes(), "-----BEGIN PUBLIC KEY-----", "-----END PUBLIC KEY-----");
            }
        }
    }

    private String formatKeyContent(byte[] keyBytes, String beginMarker, String endMarker) {
        return new String(keyBytes, StandardCharsets.UTF_8).replace(beginMarker, "")
            .replace(endMarker, "")
            .replaceAll("\\s", "");
    }

}