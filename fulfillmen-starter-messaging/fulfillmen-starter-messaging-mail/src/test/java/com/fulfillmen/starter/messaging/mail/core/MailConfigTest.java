/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.messaging.mail.core;

import java.nio.charset.StandardCharsets;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * 邮件配置测试类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class MailConfigTest {

    @BeforeEach
    void setUp() {
        // 不需要做任何准备工作
    }

    @Test
    void testDefaultValues() {
        MailConfig config = new MailConfig();

        assertEquals("smtp", config.getProtocol());
        assertEquals(StandardCharsets.UTF_8, config.getDefaultEncoding());
        assertFalse(config.isSslEnabled());
        assertTrue(config.getProperties().isEmpty());
    }

    @Test
    void testSettersAndGetters() {
        MailConfig config = new MailConfig();

        config.setProtocol("smtp");
        config.setHost("smtp.example.com");
        config.setPort(25);
        config.setUsername("<EMAIL>");
        config.setPassword("password");
        config.setFrom("<EMAIL>");
        config.setSslEnabled(true);
        config.setSslPort(465);
        config.setDefaultEncoding(StandardCharsets.UTF_8);

        assertEquals("smtp", config.getProtocol());
        assertEquals("smtp.example.com", config.getHost());
        assertEquals(25, config.getPort());
        assertEquals("<EMAIL>", config.getUsername());
        assertEquals("password", config.getPassword());
        assertEquals("<EMAIL>", config.getFrom());
        assertTrue(config.isSslEnabled());
        assertEquals(465, config.getSslPort());
        assertEquals(StandardCharsets.UTF_8, config.getDefaultEncoding());
    }

    @Test
    void testToJavaMailProperties() {
        MailConfig config = new MailConfig();
        config.setFrom("<EMAIL>");
        config.setSslEnabled(true);
        config.setSslPort(465);

        Properties props = config.toJavaMailProperties();

        assertEquals("<EMAIL>", props.getProperty("mail.from"));
        assertEquals("true", props.getProperty("mail.smtp.auth"));
        assertEquals("true", props.getProperty("mail.smtp.ssl.enable"));
        assertEquals("465", props.get("mail.smtp.socketFactory.port").toString());
        assertEquals("javax.net.ssl.SSLSocketFactory", props.getProperty("mail.smtp.socketFactory.class"));
    }

    @Test
    void testToJavaMailPropertiesWithoutSsl() {
        MailConfig config = new MailConfig();
        config.setFrom("<EMAIL>");
        config.setSslEnabled(false);

        Properties props = config.toJavaMailProperties();

        assertEquals("<EMAIL>", props.getProperty("mail.from"));
        assertEquals("true", props.getProperty("mail.smtp.auth"));
        assertEquals("false", props.getProperty("mail.smtp.ssl.enable"));
        assertNull(props.getProperty("mail.smtp.socketFactory.port"));
        assertNull(props.getProperty("mail.smtp.socketFactory.class"));
    }

    @Test
    void testCustomProperties() {
        MailConfig config = new MailConfig();
        config.setFrom("<EMAIL>"); // 确保from不为null
        config.getProperties().put("mail.smtp.timeout", "5000");
        config.getProperties().put("mail.smtp.connectiontimeout", "5000");

        Properties props = config.toJavaMailProperties();

        assertEquals("5000", props.getProperty("mail.smtp.timeout"));
        assertEquals("5000", props.getProperty("mail.smtp.connectiontimeout"));
    }
}