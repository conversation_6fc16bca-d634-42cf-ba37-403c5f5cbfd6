/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * WMS 订单状态枚举
 *
 * <AUTHOR>
 * @date 2025/7/9
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum WmsOrderStatusEnum {

    /**
     * 已付待审
     */
    PAID_PENDING_REVIEW(0, "已付待审"),

    /**
     * 已审待采
     */
    REVIEWED_PENDING_PURCHASE(1, "已审待采"),

    /**
     * 已采待发
     */
    PURCHASED_PENDING_SHIPMENT(2, "已采待发"),

    /**
     * 已发待收
     */
    SHIPPED_PENDING_RECEIPT(3, "已发待收"),

    /**
     * 待签收
     */
    PENDING_SIGNATURE(4, "待签收"),

    /**
     * 已签收
     */
    SIGNED(5, "已签收"),

    /**
     * 已完成
     */
    COMPLETED(6, "已完成"),

    /**
     * 发货异常
     */
    SHIPMENT_EXCEPTION(7, "发货异常"),

    /**
     * 已作废
     */
    CANCELED(8, "已作废"),

    /**
     * 待付款
     */
    PENDING_PAYMENT(9, "待付款");

    @JsonValue
    private final int code;

    private final String description;

    WmsOrderStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

}
