2025-07-28 18:31:17 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-28 18:31:17 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 34071 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-07-28 18:31:17 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-07-28 18:31:17 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "local"
2025-07-28 18:31:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 18:31:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 18:31:18 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-07-28 18:31:19 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-07-28 18:31:19 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 18:31:19 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-07-28 18:31:19 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-07-28 18:31:19 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-07-28 18:31:19 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-07-28 18:31:20 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2531 ms
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-07-28 18:31:20 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-07-28 18:31:20 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-07-28 18:31:20 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-07-28 18:31:20 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-07-28 18:31:20 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-07-28 18:31:21 INFO  [redisson-netty-1-4] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-28 18:31:21 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ThreadPool' completed initialization.
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - ThreadPool extension configuration applied: coreSize=12, maxSize=24, queueCapacity=2147483647, threadNamePrefix=naya-task-pool, rejectedPolicy=CALLER_RUNS
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS WebClient: baseUrl=[https://wms.fulfillmen.com]
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS声明式HTTP接口
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [AUTO, DISABLED, MANUAL]
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - 支持的消息类型: [ORDER_BUYER_VIEW_BUYER_MAKE, ORDER_BUYER_VIEW_ORDER_PAY, ORDER_BATCH_PAY, ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS, ORDER_BUYER_VIEW_PART_PART_SENDGOODS, ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, ORDER_BUYER_VIEW_ORDER_SUCCESS, ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY]
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.autoconfigure.ValidatorAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Validator' completed initialization.
2025-07-28 18:31:21 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-07-28 18:31:21 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-07-28 18:31:22 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-28 18:31:22 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-28 18:31:22 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@2881ad47
2025-07-28 18:31:22 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:34071, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=7, lastTimeStamp=1753698682457}] - instanceId:[InstanceId{instanceId=**************:34071, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-07-28 18:31:22 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - TaskScheduler extension configuration applied: poolSize=12, threadNamePrefix=scheduling-, rejectedPolicy=CALLER_RUNS
2025-07-28 18:31:23 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 56 ms
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-07-28 18:31:23 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 6.262 seconds (process running for 7.105)
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-07-28 18:31:23 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-07-28 18:31:23 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到USD的汇率
2025-07-28 18:31:23 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到USD的汇率
2025-07-28 18:31:23 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=USD
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:23 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=USD
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:23 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:23 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=c29c301175c288706b51b74fb6aea74eea716f91097f3bfcdcb5ee7c3e971f3e; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"USD","currencyT_Name":"美元","currencyFD":"1","exchange":"0.1394","result":"0.1394","updateTime":"2025-07-28 18:26:00"},{"currencyF":"USD","currencyF_Name":"美元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"7.1741","result":"7.1741","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:23 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:23 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=733d2c325e6eb7d9f73b8e8ef232c432ded6b3b7b62588b236fe1c6adec5afb3; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"USD","currencyT_Name":"美元","currencyFD":"1","exchange":"0.1394","result":"0.1394","updateTime":"2025-07-28 18:26:00"},{"currencyF":"USD","currencyF_Name":"美元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"7.1741","result":"7.1741","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:23 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:23 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:23 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> USD
2025-07-28 18:31:23 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> USD
2025-07-28 18:31:23 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-07-28 18:31:23 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-07-28 18:31:23 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-07-28 18:31:23 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-07-28 18:31:24 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到EUR的汇率
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=EUR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:24 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到EUR的汇率
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=EUR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:24 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:24 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=908f9846603d3528b62ce9de4024742c2a160a37964250b66d11821b52aefc38; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"EUR","currencyT_Name":"欧元","currencyFD":"1","exchange":"0.1196","result":"0.1196","updateTime":"2025-07-28 18:26:00"},{"currencyF":"EUR","currencyF_Name":"欧元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"8.3645","result":"8.3645","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:24 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:24 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=c02fd8cca138247cf98097bfe4c28c6db73c9f151dad43c3d9babb4b0108ef36; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"EUR","currencyT_Name":"欧元","currencyFD":"1","exchange":"0.1196","result":"0.1196","updateTime":"2025-07-28 18:26:00"},{"currencyF":"EUR","currencyF_Name":"欧元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"8.3645","result":"8.3645","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:24 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:24 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> EUR
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> EUR
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-07-28 18:31:24 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到JPY的汇率
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=JPY
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:24 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到JPY的汇率
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=JPY
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:24 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:24 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=36f6e639ea0d2e7a3c3f5ae78355fb108a06c74ee2101cbbed83a50bb06f9d80; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"JPY","currencyT_Name":"日元","currencyFD":"1","exchange":"20.6863","result":"20.6863","updateTime":"2025-07-28 18:26:00"},{"currencyF":"JPY","currencyF_Name":"日元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.04834","result":"0.04834","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:24 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> JPY
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.68
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.68 (原始: 20.68)
2025-07-28 18:31:24 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:24 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=6a642dc379108b14b3ae89b9892c7eeccb7b0f6deb2c134d86b16ada7f18dbfa; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"JPY","currencyT_Name":"日元","currencyFD":"1","exchange":"20.6863","result":"20.6863","updateTime":"2025-07-28 18:26:00"},{"currencyF":"JPY","currencyF_Name":"日元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.04834","result":"0.04834","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:24 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> JPY
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.68
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.68 (原始: 20.68)
2025-07-28 18:31:24 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到KRW的汇率
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=KRW
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:24 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到KRW的汇率
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=KRW
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:24 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:24 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=3770e81f3512ea28d5880157ada4d3f8d2000ec6d19bfc1a733a84dd603aff65; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"successed","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"KRW","currencyT_Name":"韩元","currencyFD":"1","exchange":"193.2655","result":"193.2655","updateTime":"2025-07-28 18:29:00"},{"currencyF":"KRW","currencyF_Name":"韩元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.005174","result":"0.005174","updateTime":"2025-07-28 18:29:00"}],"error_code":0}

2025-07-28 18:31:24 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: successed
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> KRW
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 193.26
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 193.26 (原始: 193.26)
2025-07-28 18:31:24 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:24 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=52b600cecc62f2a7182d2ca85a8d255773a0f1c5b87addc3baca2828285a051a; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"KRW","currencyT_Name":"韩元","currencyFD":"1","exchange":"193.2655","result":"193.2655","updateTime":"2025-07-28 18:26:00"},{"currencyF":"KRW","currencyF_Name":"韩元","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.005174","result":"0.005174","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:24 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> KRW
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 193.26
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 193.26 (原始: 193.26)
2025-07-28 18:31:24 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到INR的汇率
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=INR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:24 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 调用汇率API获取CNY到INR的汇率
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [请求信息]
URL: http://op.juhe.cn/onebox/exchange/currency?key=********************************&version=2&from=CNY&to=INR
Method: GET
Headers: [Content-Type:"application/json", Accept:"application/json"]

2025-07-28 18:31:24 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:24 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=0b29e8758e58759d029f4738564c380244e7da08787ff9db7eafd852a9781a07; Path=/; HttpOnly", Etag:"973748d511f7b572138de007096af38e"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"INR","currencyT_Name":"印度卢比","currencyFD":"1","exchange":"12.0794","result":"12.0794","updateTime":"2025-07-28 18:26:00"},{"currencyF":"INR","currencyF_Name":"印度卢比","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.08279","result":"0.08279","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:24 DEBUG [reactor-http-nio-16] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> INR
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.07
2025-07-28 18:31:24 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.07 (原始: 12.07)
2025-07-28 18:31:24 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.fulfillmen.shop.common.config.WebClientBuilder - [响应信息]
Status: 200 OK
Headers: [Date:"Mon, 28 Jul 2025 10:31:24 GMT", Content-Type:"application/json;charset=utf-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Set-Cookie:"aliyungf_tc=20d0f0d4cceb3dc25aa9088c58152ebce1cdd5ecdb71e6d8b28c234a05f5d89c; Path=/; HttpOnly", Etag:"5570ab0d508bedb604e6a9859eb8654b"]
Body: {"reason":"查询成功!","result":[{"currencyF":"CNY","currencyF_Name":"人民币","currencyT":"INR","currencyT_Name":"印度卢比","currencyFD":"1","exchange":"12.0794","result":"12.0794","updateTime":"2025-07-28 18:26:00"},{"currencyF":"INR","currencyF_Name":"印度卢比","currencyT":"CNY","currencyT_Name":"人民币","currencyFD":"1","exchange":"0.08279","result":"0.08279","updateTime":"2025-07-28 18:26:00"}],"error_code":0}

2025-07-28 18:31:24 DEBUG [reactor-http-nio-14] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率API调用成功: 查询成功!
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 汇率数据已缓存: CNY -> INR
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.07
2025-07-28 18:31:24 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.07 (原始: 12.07)
2025-07-28 18:31:25 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-28 18:31:25 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-28 18:31:25 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-07-28 18:31:25 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-07-28 18:31:25 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-07-28 18:31:25 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-07-28 18:31:25 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-28 18:31:25 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 18:31:25 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-28 18:31:25 INFO  [RMI TCP Connection(4)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-28 18:31:25 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@6865ddb3
2025-07-28 18:31:25 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-28 18:45:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-28 18:31:21,327 to 2025-07-28 18:45:00,013
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.01|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.01|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.01|  0.00%|            10|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-28 18:53:19 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m11s312ms).
2025-07-28 18:55:22 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m2s469ms).
2025-07-28 18:58:17 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m24s765ms).
2025-07-28 19:00:50 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m33s201ms).
2025-07-28 19:02:55 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m34s504ms).
2025-07-28 19:07:07 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m18s273ms).
2025-07-28 19:09:45 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m28s109ms).
2025-07-28 19:09:48 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-07-28 18:45:00,013 to 2025-07-28 19:09:48,947
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._local       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-07-28 19:11:21 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m6s100ms).
2025-07-28 19:12:50 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=58s53ms).
2025-07-28 19:15:55 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m5s326ms).
2025-07-28 19:32:59 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m33s840ms).
2025-07-28 20:26:43 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=53m44s99ms).
2025-07-28 20:44:36 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m52s949ms).
2025-07-28 21:53:39 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h9m3s187ms).
2025-07-28 22:00:05 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=6m25s388ms).
2025-07-28 22:02:37 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=2m31s808ms).
2025-07-28 22:07:21 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-07-28 22:07:21 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=7, lastTimeStamp=1753711641949}] instanceId:[InstanceId{instanceId=**************:34071, stable=false}] @ namespace:[fulfillmen-shop].
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-28 22:07:21 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-28 22:07:24 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-07-28 22:07:24 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-07-28 22:07:24 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-28 22:07:24 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
