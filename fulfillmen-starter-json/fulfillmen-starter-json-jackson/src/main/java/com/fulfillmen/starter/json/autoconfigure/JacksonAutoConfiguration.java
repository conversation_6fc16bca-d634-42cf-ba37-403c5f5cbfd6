/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.json.autoconfigure;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fulfillmen.starter.json.jackson.MultiFormatLocalDateTimeDeserializer;
import com.fulfillmen.starter.core.enums.BaseEnum;
import com.fulfillmen.starter.core.util.GeneralPropertySourceFactory;
import com.fulfillmen.starter.json.config.JacksonTimeFormatProperties;
import com.fulfillmen.starter.json.serializer.BaseEnumDeserializer;
import com.fulfillmen.starter.json.serializer.BaseEnumSerializer;
import com.fulfillmen.starter.json.serializer.BigNumberSerializer;
import com.fulfillmen.starter.json.serializer.SimpleDeserializersWrapper;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.PropertySource;

/**
 * Jackson 自动配置
 *
 * <AUTHOR>
 * @date 2024/12/16 11:36
 * @description: todo
 * @since 1.0.0
 */
@AutoConfiguration
@EnableConfigurationProperties(JacksonTimeFormatProperties.class)
@PropertySource(value = "classpath:default-json-jackson.yml", factory = GeneralPropertySourceFactory.class)
public class JacksonAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(JacksonAutoConfiguration.class);

    @Bean
    @ConditionalOnMissingBean(ObjectMapper.class)
    public ObjectMapper objectMapper() {
        log.debug("[Fulfillmen Starter] - Auto Configuration 'Jackson' completed initialization.");
        return JsonMapper.builder()
            // 基础配置
            .enable(MapperFeature.PROPAGATE_TRANSIENT_MARKER)
            .enable(JsonParser.Feature.ALLOW_COMMENTS)
            .enable(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES)
            .enable(JsonParser.Feature.ALLOW_SINGLE_QUOTES)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .serializationInclusion(Include.NON_NULL)
            // 注册模块
            .addModule(timeModule())
            .addModule(simpleModule())
            // 添加 JDK8 模块支持
            .addModule(new Jdk8Module())
            // 日期时间格式配置（禁用时间戳格式）- 禁用时间戳特性，使用 ISO-8601 日期格式
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .build();
    }

    /**
     * 日期时间模块配置
     *
     * <p>配置支持多种时间格式的反序列化器，包括：</p>
     * <ul>
     * <li>ISO 8601 带毫秒格式：2025-07-24T16:37:32.957</li>
     * <li>ISO 8601 标准格式：2025-07-24T16:37:32</li>
     * <li>中国标准格式：2025-07-24 16:37:32</li>
     * <li>带毫秒的中国格式：2025-07-24 16:37:32.957</li>
     * </ul>
     */
    private JavaTimeModule timeModule() {
        JavaTimeModule module = new JavaTimeModule();

        // LocalDateTime - 使用多格式反序列化器，序列化保持统一格式
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
        module.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        module.addDeserializer(LocalDateTime.class, new MultiFormatLocalDateTimeDeserializer());

        // LocalDate - 保持原有配置
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN);
        module.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
        module.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));

        // LocalTime - 保持原有配置
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(DatePattern.NORM_TIME_PATTERN);
        module.addSerializer(LocalTime.class, new LocalTimeSerializer(timeFormatter));
        module.addDeserializer(LocalTime.class, new LocalTimeDeserializer(timeFormatter));

        // 大数值处理
        module.addSerializer(Long.class, BigNumberSerializer.SERIALIZER_INSTANCE);
        module.addSerializer(Long.TYPE, BigNumberSerializer.SERIALIZER_INSTANCE);
        module.addSerializer(BigInteger.class, BigNumberSerializer.SERIALIZER_INSTANCE);

        return module;
    }

    /**
     * 枚举模块配置
     */
    private SimpleModule simpleModule() {
        SimpleModule simpleModule = new SimpleModule();
        simpleModule
            .addSerializer((Class<BaseEnum<?>>)(Class<?>)BaseEnum.class, BaseEnumSerializer.SERIALIZER_INSTANCE);

        SimpleDeserializersWrapper deserializers = new SimpleDeserializersWrapper();
        // 添加类型通配符并调整泛型参数
        deserializers
            .addDeserializer((Class<BaseEnum<?>>)(Class<?>)BaseEnum.class, (JsonDeserializer<? extends BaseEnum<?>>)BaseEnumDeserializer.SERIALIZER_INSTANCE);
        simpleModule.setDeserializers(deserializers);
        return simpleModule;
    }
}