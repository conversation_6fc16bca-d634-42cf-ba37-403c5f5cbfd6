/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.log.interceptor.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Set;
import java.util.Vector;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.method.HandlerMethod;

import com.fulfillmen.starter.log.core.dao.LogDao;
import com.fulfillmen.starter.log.core.enums.Include;
import com.fulfillmen.starter.log.core.model.LogRecord;
import com.fulfillmen.starter.log.interceptor.annotation.Log;
import com.fulfillmen.starter.log.interceptor.autoconfigure.LogProperties;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * LogInterceptor 测试类
 *
 * <AUTHOR>
 * @date 2025/01/27 22:45
 * @description: LogInterceptor 单元测试
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class LogInterceptorTest {

    @Mock
    private LogDao logDao;
    @Mock
    private LogProperties logProperties;
    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;
    @Mock
    private HandlerMethod handlerMethod;

    private LogInterceptor logInterceptor;
    private TestController testController;

    @BeforeEach
    void setUp() throws Exception {
        lenient().when(request.getHeaderNames()).thenReturn(new Vector<String>().elements());
        Set<Include> defaultIncludes = new HashSet<>();
        defaultIncludes.add(Include.MODULE);
        defaultIncludes.add(Include.DESCRIPTION);
        lenient().when(logProperties.getIncludes()).thenReturn(defaultIncludes);
        logInterceptor = new LogInterceptor(logDao, logProperties);
        testController = new TestController();

        // 基础设置
        lenient().when(request.getMethod()).thenReturn("GET");
        lenient().when(request.getRequestURI()).thenReturn("/test");
        lenient().when(request.getRequestURL()).thenReturn(new StringBuffer("http://localhost:8080/test"));
        lenient().when(response.getStatus()).thenReturn(200);
        lenient().when(logProperties.isMatch(anyString())).thenReturn(false);
        lenient().when(logProperties.getIsPrint()).thenReturn(true);

        // 默认返回 TestController.class 作为 bean type
        lenient().doReturn(TestController.class).when(handlerMethod).getBeanType();

        // 默认注解返回值
        lenient().doReturn(null).when(handlerMethod).getMethodAnnotation(Operation.class);
        lenient().doReturn(null).when(handlerMethod).getMethodAnnotation(Hidden.class);
        lenient().doReturn(null).when(handlerMethod).getMethodAnnotation(Log.class);
    }

    @Test
    void whenMethodHasLogModuleThenShouldUseMethodModule() throws Exception {
        // Given
        Method method = TestController.class.getMethod("methodWithModule");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(method.getAnnotation(Log.class)).when(handlerMethod).getMethodAnnotation(Log.class);

        // When
        logInterceptor.preHandle(request, response, handlerMethod);
        logInterceptor.afterCompletion(request, response, handlerMethod, null);

        // Then
        ArgumentCaptor<LogRecord> logRecordCaptor = ArgumentCaptor.forClass(LogRecord.class);
        verify(logDao).add(logRecordCaptor.capture());
        assertEquals("测试模块", logRecordCaptor.getValue().getModule());
    }

    @Test
    void whenClassHasLogModuleThenShouldUseClassModule() throws Exception {
        // Given
        TestControllerWithClassModule controller = new TestControllerWithClassModule();
        Method method = TestControllerWithClassModule.class.getMethod("testMethod");
        lenient().doReturn(controller).when(handlerMethod).getBean();
        lenient().doReturn(TestControllerWithClassModule.class).when(handlerMethod).getBeanType();
        lenient().doReturn(method).when(handlerMethod).getMethod();

        // When
        logInterceptor.preHandle(request, response, handlerMethod);
        logInterceptor.afterCompletion(request, response, handlerMethod, null);

        // Then
        ArgumentCaptor<LogRecord> logRecordCaptor = ArgumentCaptor.forClass(LogRecord.class);
        verify(logDao).add(logRecordCaptor.capture());
        assertEquals("类级模块", logRecordCaptor.getValue().getModule());
    }

    @Test
    void whenMethodHasLogDescriptionThenShouldUseDescription() throws Exception {
        // Given
        Method method = TestController.class.getMethod("methodWithDescription");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(method.getAnnotation(Log.class)).when(handlerMethod).getMethodAnnotation(Log.class);

        // When
        logInterceptor.preHandle(request, response, handlerMethod);
        logInterceptor.afterCompletion(request, response, handlerMethod, null);

        // Then
        ArgumentCaptor<LogRecord> logRecordCaptor = ArgumentCaptor.forClass(LogRecord.class);
        verify(logDao).add(logRecordCaptor.capture());
        assertEquals("测试描述", logRecordCaptor.getValue().getDescription());
    }

    @Test
    void whenMethodHasOperationSummaryThenShouldUseAsSummary() throws Exception {
        // Given
        Method method = TestController.class.getMethod("methodWithOperation");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(method.getAnnotation(Operation.class))
            .when(handlerMethod)
            .getMethodAnnotation(Operation.class);
        lenient().doReturn(null).when(handlerMethod).getMethodAnnotation(Log.class);

        // When
        logInterceptor.preHandle(request, response, handlerMethod);
        logInterceptor.afterCompletion(request, response, handlerMethod, null);

        // Then
        ArgumentCaptor<LogRecord> logRecordCaptor = ArgumentCaptor.forClass(LogRecord.class);
        verify(logDao).add(logRecordCaptor.capture());
        assertEquals("操作描述", logRecordCaptor.getValue().getDescription());
    }

    @Test
    void whenClassHasTagThenShouldUseTagName() throws Exception {
        // Given
        TestControllerWithTag controller = new TestControllerWithTag();
        Method method = TestControllerWithTag.class.getMethod("testMethod");
        lenient().doReturn(controller).when(handlerMethod).getBean();
        lenient().doReturn(TestControllerWithTag.class).when(handlerMethod).getBeanType();
        lenient().doReturn(method).when(handlerMethod).getMethod();

        // When
        logInterceptor.preHandle(request, response, handlerMethod);
        logInterceptor.afterCompletion(request, response, handlerMethod, null);

        // Then
        ArgumentCaptor<LogRecord> logRecordCaptor = ArgumentCaptor.forClass(LogRecord.class);
        verify(logDao).add(logRecordCaptor.capture());
        assertEquals("标签模块", logRecordCaptor.getValue().getModule());
    }

    @Test
    void whenHandlerHasLogAnnotationThenShouldProceed() throws Exception {
        // Given
        Method method = TestController.class.getMethod("annotatedMethod");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(method.getAnnotation(Log.class)).when(handlerMethod).getMethodAnnotation(Log.class);

        // When
        boolean result = logInterceptor.preHandle(request, response, handlerMethod);

        // Then
        assertTrue(result);
    }

    @Test
    void whenHandlerDoesNotHaveLogAnnotationThenShouldProceed() throws Exception {
        // Given
        Method method = TestController.class.getMethod("nonAnnotatedMethod");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(null).when(handlerMethod).getMethodAnnotation(Log.class);

        // When
        boolean result = logInterceptor.preHandle(request, response, handlerMethod);

        // Then
        assertTrue(result);
    }

    @Test
    void whenRequestCompletedThenLogRecordShouldBeSaved() throws Exception {
        // Given
        Method method = TestController.class.getMethod("annotatedMethod");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(method.getAnnotation(Log.class)).when(handlerMethod).getMethodAnnotation(Log.class);

        // When
        logInterceptor.preHandle(request, response, handlerMethod);
        logInterceptor.afterCompletion(request, response, handlerMethod, null);

        // Then
        verify(logDao).add(any(LogRecord.class));
    }

    @Test
    void whenExceptionOccursThenErrorMessageShouldBeLogged() throws Exception {
        // Given
        Method method = TestController.class.getMethod("annotatedMethod");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(method.getAnnotation(Log.class)).when(handlerMethod).getMethodAnnotation(Log.class);
        Exception testException = new RuntimeException("Test Exception");

        // When
        logInterceptor.preHandle(request, response, handlerMethod);
        logInterceptor.afterCompletion(request, response, handlerMethod, testException);

        // Then
        ArgumentCaptor<LogRecord> logRecordCaptor = ArgumentCaptor.forClass(LogRecord.class);
        verify(logDao).add(logRecordCaptor.capture());

        LogRecord capturedRecord = logRecordCaptor.getValue();
        assertNotNull(capturedRecord);
        assertNotNull(capturedRecord.getErrorMsg());
        assertTrue(capturedRecord.getErrorMsg().contains("Test Exception"));
    }

    @Test
    void whenMethodHasHiddenAnnotationThenShouldSkipLogging() throws Exception {
        // Given
        Method method = TestController.class.getMethod("hiddenMethod");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(method.getAnnotation(Hidden.class)).when(handlerMethod).getMethodAnnotation(Hidden.class);

        // When
        boolean result = logInterceptor.preHandle(request, response, handlerMethod);

        // Then
        assertTrue(result);
        verify(logDao, never()).add(any(LogRecord.class));
    }

    @Test
    void whenLogPropertiesChangesDuringRequestThenShouldRespectNewSettings() throws Exception {
        // Given
        Method method = TestController.class.getMethod("annotatedMethod");
        lenient().doReturn(method).when(handlerMethod).getMethod();
        lenient().doReturn(method.getAnnotation(Log.class)).when(handlerMethod).getMethodAnnotation(Log.class);
        lenient().when(logProperties.getIsPrint()).thenReturn(true, false);

        // When & Then
        logInterceptor.preHandle(request, response, handlerMethod);
        verify(logProperties, times(1)).getIsPrint();

        logInterceptor.afterCompletion(request, response, handlerMethod, null);
        verify(logProperties, times(2)).getIsPrint();
    }

    // Test controller for handler method testing
    private static class TestController {
        @Log(value = "Test Operation")
        public void annotatedMethod() {
        }

        public void nonAnnotatedMethod() {
        }

        @Hidden
        public void hiddenMethod() {
        }

        @Log(ignore = true)
        public void ignoredMethod() {
        }

        @Log(module = "测试模块")
        public void methodWithModule() {
        }

        @Log("测试描述")
        public void methodWithDescription() {
        }

        @Operation(summary = "操作描述")
        public void methodWithOperation() {
        }
    }

    @Log(module = "类级模块")
    private static class TestControllerWithClassModule {
        public void testMethod() {
        }
    }

    @Tag(name = "标签模块")
    private static class TestControllerWithTag {
        public void testMethod() {
        }
    }
}