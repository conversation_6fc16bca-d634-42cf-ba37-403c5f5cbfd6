/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes.FailedOrderInfo;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes.OrderIdsInfo;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseDataRes.SuccessOrderInfo;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 创建 wms 订单事件
 *
 * <AUTHOR>
 * @date 2025/7/18 17:54
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreatePurchaseOrderSyncWmsHandler {

    private final TzOrderSupplierMapper orderSupplierMapper;
    private final IWmsManager wmsManager;

    /**
     * 处理订单创建事件
     *
     * @param orderContext 订单上下文
     */
    public void handle(OrderContext orderContext) {
        log.info("开始创建 1688 订单，采购订单号: {}", orderContext.getPurchaseOrderNo());
        try {
            // TODO: 2025/7/18 可以先通过 供应商订单号，进行查询。如果已经同步过，则不需要创建，仅同步
            syncToWms(orderContext);
        } catch (Exception e) {
            log.error("同步 wms 订单失败 : [{}] ", orderContext.getPurchaseOrderNo(), e);
        }
    }

    /**
     * 同步采购订单到 WMS
     */
    private void syncToWms(OrderContext orderContext) {
        String purchaseOrderNo = orderContext.getPurchaseOrder().getPurchaseOrderNo();
        // TODO: 2025/7/11 同步 wms 采购订单
        // 如果用户未绑定 WMS 账户，则获取租户的信息来采购订单
        String cusCode = UserContextHolder.getWmsCusCodeOrTenantCusCode();

        if (!StringUtils.hasText(cusCode)) {
            log.error("无法获取 cusCode , 找不到 WMS 账户信息，无法创建 Wms 采购订单。请检查一下配置信息。");
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.UNABLE_RETRIEVE_WMS_ACCOUNT);
        }
        // 调用 API 接口
        Optional<WmsPurchaseDataRes> wmsPurchaseOrder = wmsManager.createWmsPurchaseOrder(orderContext, cusCode);
        if (wmsPurchaseOrder.isEmpty()) {
            log.error("wms 订单创建失败 : [{}] ", purchaseOrderNo);
            return;
        }
        log.info("wms 订单创建成功 : [{}] ", wmsPurchaseOrder);
        // 获取创建成功订单列表
        List<SuccessOrderInfo> successOrderList = wmsPurchaseOrder.map(WmsPurchaseDataRes::getOrderIds)
            .map(OrderIdsInfo::getSuccessOrders).orElse(Lists.newArrayList());
        // 获取失败的订单列表
        List<FailedOrderInfo> failedOrderList = wmsPurchaseOrder.map(WmsPurchaseDataRes::getOrderIds)
            .map(OrderIdsInfo::getFailedOrders).orElse(Lists.newArrayList());
        updateFailedOrderStatus(failedOrderList);
        updateSuccessOrderStatus(successOrderList);
    }

    /**
     * 更新失败的订单状态
     */
    private void updateFailedOrderStatus(List<FailedOrderInfo> failedOrderInfos) {
        if (CollectionUtils.isEmpty(failedOrderInfos)) {
            return;
        }
        Set<String> supplierNos = failedOrderInfos.stream().map(FailedOrderInfo::getShopOrderId).collect(Collectors.toSet());
        // 根据供应商订单号查询供应商订单
        Map<String, TzOrderSupplier> orderSupplierMap = this.orderSupplierMapper.selectList(
            new LambdaQueryWrapper<TzOrderSupplier>().in(TzOrderSupplier::getSupplierOrderNo, supplierNos)).stream()
            .collect(Collectors.toMap(TzOrderSupplier::getSupplierOrderNo, orderSupplier -> orderSupplier));
        // 根据 shopOrderIdToPurchaseNoMap 更新供应商订单
        log.info("处理 wms 创建失败的订单 : [{}] ", supplierNos);
        List<TzOrderSupplier> orderSuppliers = Lists.newArrayList();
        failedOrderInfos.forEach(failedOrderInfo -> {
            TzOrderSupplier orderSupplier = orderSupplierMap.get(failedOrderInfo.getShopOrderId());
            String failedMessage = String.format("同步 wms 采购订单失败, 状态: [%s], 原因: [%s]", failedOrderInfo.getStatus(), failedOrderInfo.getDesc());
            orderSupplier.setWmsFailedMessage(failedMessage);
            TzOrderSupplier updateOrderSupplier = TzOrderSupplier.builder()
                .id(orderSupplier.getId())
                .wmsSyncStatus(OrderSupplierSyncStatusEnums.SYNC_FAILED)
                .wmsFailedMessage(failedMessage)
                .build();
            orderSuppliers.add(updateOrderSupplier);
        });
        if (CollectionUtils.isEmpty(orderSuppliers)) {
            log.warn("更新数量空 : [{}] ", orderSuppliers);
            return;
        }
        // 批量更新
        this.orderSupplierMapper.updateById(orderSuppliers);
    }

    /**
     * 更新成功的订单状态
     */
    private void updateSuccessOrderStatus(List<SuccessOrderInfo> successOrderInfos) {
        if (CollectionUtils.isEmpty(successOrderInfos)) {
            return;
        }
        // 获取 shopOrderId 分组 key 是 shopOrderId value 是 purchaseNo
        Map<String, String> shopOrderIdToPurchaseNoMap = successOrderInfos.stream()
            .collect(Collectors.toMap(SuccessOrderInfo::getShopOrderId, SuccessOrderInfo::getPurchaseNo));
        // 获取所有的 供应商订单
        Set<String> supplierNos = shopOrderIdToPurchaseNoMap.keySet();
        // 根据供应商订单号查询供应商订单
        List<TzOrderSupplier> orderSuppliersSuccess = this.orderSupplierMapper.selectList(
            new LambdaQueryWrapper<TzOrderSupplier>().in(TzOrderSupplier::getSupplierOrderNo, supplierNos));
        // 根据 shopOrderIdToPurchaseNoMap 更新供应商订单
        log.info("wms 订单创建成功，更新供应商订单 : [{}] ", shopOrderIdToPurchaseNoMap);
        List<TzOrderSupplier> orderSuppliers = Lists.newArrayList();
        orderSuppliersSuccess.forEach(orderSupplier -> {
            String purchaseNo = shopOrderIdToPurchaseNoMap.get(orderSupplier.getSupplierOrderNo());
            TzOrderSupplier updateOrderSupplier = TzOrderSupplier.builder()
                .id(orderSupplier.getId())
                .wmsPurchaseOrderNo(purchaseNo)
                .wmsSyncStatus(OrderSupplierSyncStatusEnums.SYNCED)
                .build();
            orderSuppliers.add(updateOrderSupplier);
        });
        if (CollectionUtils.isEmpty(orderSuppliers)) {
            log.warn("更新数量空 : [{}] ", orderSuppliers);
            return;
        }
        // 批量更新
        this.orderSupplierMapper.updateBatchById(orderSuppliers);
    }
}
