/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.impl;

import com.fulfillmen.shop.manager.support.alibaba.IToolsManager;
import com.fulfillmen.support.alibaba.api.request.tools.WangwangNickDecryptRequestRecord;
import com.fulfillmen.support.alibaba.service.IAiCapabilityService;
import com.fulfillmen.support.alibaba.service.IToolsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 工具类管理 和 对应的 ai 能力管理
 *
 * <AUTHOR>
 * @date 2025/5/9 11:20
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
public class ToolsManager implements IToolsManager {

    private final IToolsService toolsService;
    private final IAiCapabilityService aiCapabilityService;

    public ToolsManager(IToolsService toolsService, IAiCapabilityService aiCapabilityService) {
        this.toolsService = toolsService;
        this.aiCapabilityService = aiCapabilityService;
    }

    @Override
    public String getWangWangNickname(String openUid) {
        try {
            return this.toolsService.decryptWangwangNick(WangwangNickDecryptRequestRecord.of(openUid))
                .<String>handle((response, sink) -> {
                    sink.next(response.getWangwangNick());
                })
                .block();
        } catch (Exception e) {
            log.error("Failed to get wangwang nickname , openUid : [{}] ", openUid, e);
        }
        return null;
    }

}
