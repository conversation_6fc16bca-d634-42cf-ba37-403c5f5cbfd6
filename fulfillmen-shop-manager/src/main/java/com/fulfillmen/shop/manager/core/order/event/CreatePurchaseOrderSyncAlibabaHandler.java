/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContext;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.dao.mapper.TenantWarehouseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.dto.order.CreateOrderRespDTO;
import com.fulfillmen.shop.domain.entity.TenantWarehouse;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierMultipleOrdersEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.manager.support.alibaba.impl.OrderManager;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.request.order.OrderCreateRequestRecord;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 创建 1688 订单事件
 *
 * <AUTHOR>
 * @date 2025/7/18 17:54
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CreatePurchaseOrderSyncAlibabaHandler {

    private final OrderManager orderManager;
    private final TzOrderItemMapper orderItemMapper;
    private final TzOrderSupplierMapper orderSupplierMapper;
    private final TenantWarehouseMapper tenantWarehouseMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 处理订单创建事件
     *
     * @param orderContext 订单上下文
     */
    public void handle(OrderContext orderContext) {
        log.info("开始创建 1688 订单，采购订单号: {}", orderContext.getPurchaseOrderNo());
        // 如果订单不是待支付状态，则不需要创建 1688 订单
        try {
            if (!orderContext.isPurchaseOrderPayCompleted()) {
                log.info("采购订单 {} 未支付，不需要创建 1688 订单", orderContext.getPurchaseOrderNo());
                return;
            }
            createAlibabaOrder(orderContext);
            log.info("创建 1688 订单完成 : [{}] ", orderContext.getPurchaseOrderNo());
        } catch (Exception e) {
            log.error("创建 1688 订单失败 : [{}] ", orderContext.getPurchaseOrderNo(), e);
            // FIXME: 2025/7/18 后期开发一个事件通知机制处理
        }
    }

    /**
     * 创建 1688订单
     *
     * @param orderContext 订单上下文
     */
    private void createAlibabaOrder(OrderContext orderContext) {
        List<CompletableFuture<Void>> futures = Lists.newArrayList();
        // 并行创建 1688 订单
        orderContext.getSupplierOrders().forEach(supplierOrder -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // 使用现有的供应商订单处理逻辑
                submitSupplierOrderToAlibaba(supplierOrder, orderContext);
            }, threadPoolTaskExecutor);
            futures.add(future);
        });
        // 等待完成
        futures.forEach(CompletableFuture::join);
    }

    /**
     * 提交供应商订单到外部平台
     */
    private void submitSupplierOrderToAlibaba(TzOrderSupplier supplierOrder, OrderContext orderContext) {
        log.info("开始提交供应商订单到外部平台，订单号: {}", supplierOrder.getSupplierOrderNo());
        try {
            // 构建1688订单创建请求
            OrderCreateRequestRecord request = buildOrderCreateRequest(supplierOrder, orderContext);
            // 调用1688订单创建API
            CreateOrderRespDTO createOrderRespDTO = orderManager.createCrossOrder(request);
            // FIXME: 2025/7/28 请注意，存在部分商品下单失败。需要额外的处理对应的 商品项信息。
            if (!CollectionUtils.isEmpty(createOrderRespDTO.getFailedOfferList())) {
                // TODO：2025/7/18 1. 采购失败的商品，需要处理相关商品逻辑 2. 采购失败的商品，需要通知用户
                log.warn("部分商品采购失败，请查看并处理 : [{}] ", JacksonUtil.toJsonString(createOrderRespDTO.getFailedOfferList()));
            }
//        // 更新外部订单信息
            String metadataJson = JacksonUtil.toJsonString(createOrderRespDTO);
            if (createOrderRespDTO.getIsMultipleOrder()) {
                supplierOrder.setIsMultipleOrders(TzOrderSupplierMultipleOrdersEnum.YES);
                // 获取多个订单 ID 使用 , 分割
                String orderIds = createOrderRespDTO.getOrderList().stream()
                  .filter(Objects::nonNull).map(CreateOrderRespDTO.AlibabaCreateOrderRespDTO::getOrderId).collect(Collectors.joining(","));
                supplierOrder.setPlatformOrderNo(orderIds);
            } else {
                supplierOrder.setIsMultipleOrders(TzOrderSupplierMultipleOrdersEnum.NO);
                supplierOrder.setPlatformOrderId(createOrderRespDTO.getOrderId());
            }
            supplierOrder.setExternalSyncStatus(OrderSupplierSyncStatusEnums.SYNCED);
            supplierOrder.setMetadataJson(metadataJson);
            // 设置金额
            // 应付金额
            supplierOrder.setPayableAmountTotal(BigDecimal.valueOf(createOrderRespDTO.getTotalSuccessAmount() / 100));
            // 运费
            supplierOrder.setPayableFreightAmount(createOrderRespDTO.getPostFee() != null ? BigDecimal.valueOf(createOrderRespDTO.getPostFee() / 100) : BigDecimal.ZERO);
            // 商品金额 = 应付金额 - 应付运费
            supplierOrder.setPayableGoodsAmount(supplierOrder.getPayableAmountTotal().subtract(supplierOrder.getPayableFreightAmount()));
            orderSupplierMapper.updateById(supplierOrder);
            log.info("供应商订单提交成功，外部订单ID: {}", createOrderRespDTO.getOrderId());
        } catch (Exception e) {
            log.warn("供应商订单 {} 处理失败, 请及时修复处理。", supplierOrder.getSupplierOrderNo(), e);
            supplierOrder.setExternalSyncStatus(OrderSupplierSyncStatusEnums.SYNC_FAILED);
            orderSupplierMapper.updateById(supplierOrder);
        }
    }

    /**
     * 构建1688订单创建请求
     */
    private OrderCreateRequestRecord buildOrderCreateRequest(TzOrderSupplier supplierOrder, OrderContext orderContext) {
        log.debug("构建1688订单创建请求，供应商订单ID: {}", supplierOrder.getId());
        // 通过租户本地线程上下文，获取默认仓库地址。
        EnhancedTenantContext.TenantWarehouseInfo defaultWarehouse = EnhancedTenantContextHolder
          .getCurrentDefaultWarehouse();
        TenantWarehouse tenantWarehouse = null;
        // 如果默认仓库为空，则从数据库中获取
        if (defaultWarehouse == null) {
            // 1. 获取租户默认仓库地址
            LambdaQueryWrapper<TenantWarehouse> queryWrapper = new LambdaQueryWrapper<>();
            tenantWarehouse = tenantWarehouseMapper.selectOne(
              queryWrapper.eq(TenantWarehouse::getTenantId, UserContextHolder.getTenantId())
                .eq(TenantWarehouse::getIsDefault, 1));
            if (tenantWarehouse == null) {
                // 请设置租户默认仓库地址，否则无法创建订单
                log.error("请设置租户默认仓库地址，租户ID: {}", UserContextHolder.getTenantId());
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.TENANT_WAREHOUSE_NOT_FOUND);
            }
            defaultWarehouse = EnhancedTenantContext.TenantWarehouseInfo.convertFrom(tenantWarehouse);
        }
        // 2. 使用默认收货地址
        OrderCreateRequestRecord.AddressParamRecord addressParam = OrderCreateRequestRecord.AddressParamRecord.builder()
          // 读取用户姓名
          .fullName(defaultWarehouse.getContactName())
          // 读取用户手机号
          .mobile(defaultWarehouse.getContactMobile())
          // 读取用户邮编
          .postCode(defaultWarehouse.getPostcode())
          // 读取用户城市
          .cityText(defaultWarehouse.getCity())
          // 读取用户省份
          .provinceText(defaultWarehouse.getProvince())
          // 读取用户区县
          .areaText(defaultWarehouse.getDistrict())
          // 读取用户详细地址
          .address(defaultWarehouse.getAddress())
          // 读取用户地区码
          .districtCode(defaultWarehouse.getDistrictCode())
          .build();

        // 3. 构建商品列表
        List<OrderCreateRequestRecord.CargoParamRecord> cargoList = buildCargoList(supplierOrder);
        // 4. 设置采购单号，为下游单号
        String outOrderId = orderContext.getPurchaseOrder().getPurchaseOrderNo();

        return OrderCreateRequestRecord.builder()
          .flow("general")
          .addressParam(addressParam)
          .cargoParamList(cargoList)
          .outOrderId(outOrderId)
          .build();
    }

    /**
     * 根据供应商订单构建商品列表
     */
    private List<OrderCreateRequestRecord.CargoParamRecord> buildCargoList(TzOrderSupplier supplierOrder) {
        // 查询供应商订单对应的商品明细
        List<TzOrderItem> orderItems = orderItemMapper.selectList(
          new LambdaQueryWrapper<TzOrderItem>().eq(TzOrderItem::getSupplierOrderId, supplierOrder.getId()));

        return orderItems.stream()
          .map(orderItem -> {
              //
              String specId = orderItem.getIsSingleItem() == TzProductSpuSingleItemEnum.YES ? null
                : orderItem.getPlatformSpecId();
              // 如果是单品，则不需要设置specId
              // TODO: 需要根据供应商订单的类型来决定是否需要设置openOfferId和outMemberId
              // .openOfferId(orderItem.getOpenOfferId())
              // .outMemberId(orderItem.getOutMemberId())
              return OrderCreateRequestRecord.CargoParamRecord.builder()
                .offerId(Long.valueOf(orderItem.getPlatformProductId()))
                // 如果是单品，则不需要设置specId
                .specId(specId)
                .quantity(orderItem.getQuantity().doubleValue())
                // TODO: 需要根据供应商订单的类型来决定是否需要设置openOfferId和outMemberId
                // .openOfferId(orderItem.getOpenOfferId())
                // .outMemberId(orderItem.getOutMemberId())
                .build();
          })
          .collect(Collectors.toList());
    }

    /**
     * 处理下单失败的商品项
     * // TODO: 2025/7/28 后续处理
     */
    private void orderItemList(List<cargoItem> cargoItemList){
        // 更新 orderItem 部分商品下单失败。

    }

    private record cargoItem(
      Long offerId,
      String specId,
      String errorCode,
      String errorMessage
    ){

    }
}
