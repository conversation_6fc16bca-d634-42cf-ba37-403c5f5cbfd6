/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.context.OrderContext;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.manager.support.alibaba.impl.OrderManager;
import com.fulfillmen.support.alibaba.api.request.order.OrderCancelRequestRecord;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * 取消采购订单同步 1688 处理器
 *
 * <AUTHOR>
 * @date 2025/7/22
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CancelPurchaseOrderSyncAlibabaHandler {

    private final OrderManager orderManager;
    private final TzOrderSupplierMapper orderSupplierMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    public void handle(OrderContext context) {
        log.info("开始取消 1688 订单，采购订单号: {}", context.getPurchaseOrderNo());
        try {
            if (!context.isPurchaseOrderPayCompleted()) {
                log.info("采购订单 {} 未支付，不需要取消 1688 订单", context.getPurchaseOrderNo());
                return;
            }
            List<TzOrderSupplier> orderSuppliers = cancelAlibabaOrders(context);
            // 如果不存在需要取消的订单，则直接返回
            if (CollectionUtils.isEmpty(orderSuppliers)) {
                log.info("没有需要取消的 1688 订单，采购订单号: {}", context.getPurchaseOrderNo());
                return;
            }
            cancelAlibabaOrders(orderSuppliers, "buyerCancel");
            log.info("取消 1688 订单完成 : [{}] ", context.getPurchaseOrderNo());
        } catch (Exception e) {
            log.error("取消 1688 订单失败 : [{}] ", context.getPurchaseOrderNo(), e);
        }
    }

    /**
     * 获取需要取消 1688 订单列表
     *
     * @param context 订单上下文
     * @return 需要取消的订单列表
     */
    private List<TzOrderSupplier> cancelAlibabaOrders(OrderContext context) {
        // 获取 供应商订单 ids
        List<Long> supplierOrderIds = context.getSupplierOrders().stream()
          .map(TzOrderSupplier::getId)
          .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplierOrderIds)) {
            return Lists.newArrayList();
        }
        // 2. 更新订单状态
        return orderSupplierMapper.selectList(
          new LambdaQueryWrapper<TzOrderSupplier>()
            .in(TzOrderSupplier::getId, supplierOrderIds)
            .isNotNull(TzOrderSupplier::getPlatformOrderId)
            .ne(TzOrderSupplier::getExternalSyncStatus, OrderSupplierSyncStatusEnums.SYNC_FAILED)
        );
    }

    /**
     * 并行取消1688订单
     *
     * @param orderSuppliers 需要取消的订单列表
     * @param cancelReason   取消原因
     */
    private void cancelAlibabaOrders(List<TzOrderSupplier> orderSuppliers, String cancelReason) {
        List<CompletableFuture<Void>> futures = Lists.newArrayList();

        // 并行取消订单
        orderSuppliers.forEach(supplierOrder -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                cancelSupplierOrderToAlibaba(supplierOrder, cancelReason);
            }, threadPoolTaskExecutor);
            futures.add(future);
        });
        futures.forEach(CompletableFuture::join);
    }

    /**
     * 取消1688订单
     *
     * @param supplierOrder 供应商订单数据
     * @param cancelReason  取消原因
     */
    private void cancelSupplierOrderToAlibaba(TzOrderSupplier supplierOrder, String cancelReason) {
        try {
            // 构建1688取消订单请求
            OrderCancelRequestRecord request = buildOrderCancelRequest(supplierOrder, cancelReason);
            // 调用1688取消订单API
            boolean cancelSuccess = orderManager.cancelOrder(request).getSuccess();
            // 更新外部订单信息
            if (cancelSuccess) {
                updateSupplierOrderStatus(supplierOrder, OrderSupplierSyncStatusEnums.SYNCED, null);
                log.info("1688订单取消成功，外部订单ID: {}", supplierOrder.getPlatformOrderId());
            } else {
                updateSupplierOrderStatus(supplierOrder, OrderSupplierSyncStatusEnums.SYNC_FAILED, "取消失败");
                log.warn("1688订单取消失败，外部订单ID: {}", supplierOrder.getPlatformOrderId());
            }
        } catch (Exception e) {
            log.error("取消1688订单失败, 供应商订单号: {} ", supplierOrder.getSupplierOrderNo(), e);
        }
    }

    /**
     * 构建1688取消订单请求
     *
     * @param supplierOrder 供应商订单数据
     * @param cancelReason  取消原因
     * @return 取消订单请求
     */
    private OrderCancelRequestRecord buildOrderCancelRequest(TzOrderSupplier supplierOrder, String cancelReason) {
        return OrderCancelRequestRecord.builder()
          .tradeId(Long.valueOf(supplierOrder.getPlatformOrderId()))
          .cancelReason(cancelReason)
          .build();
    }

    /**
     * 更新供应商订单状态
     *
     * @param orderSupplier 供应商订单
     * @param status        状态
     * @param errorMessage  错误信息
     */
    private void updateSupplierOrderStatus(TzOrderSupplier orderSupplier, OrderSupplierSyncStatusEnums status, String errorMessage) {
        try {
            TzOrderSupplier updateOrderSupplier = TzOrderSupplier.builder()
              .id(orderSupplier.getId())
              .externalSyncStatus(status)
              .externalSyncFailedMessage(errorMessage)
              .build();

            orderSupplierMapper.updateById(updateOrderSupplier);
        } catch (Exception e) {
            log.error("更新供应商订单状态失败，订单ID: {}", orderSupplier.getId(), e);
        }
    }
}
