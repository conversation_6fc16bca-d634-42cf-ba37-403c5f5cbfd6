/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms.convert;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailsReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderReq;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import org.mapstruct.Mapper;

/**
 * 采购订单转换器
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper(componentModel = "spring")
public interface WmsPurchaseOrderConvert {

    /**
     * 将供应商的采购单转换成 wms 采购订单请求
     *
     * <pre>
     * 创建供应商的采购单
     * </pre>
     *
     * @param purchaseOrder 采购单
     * @param supplierOrder 供应商采购单
     * @param orderItems    订单商品列表
     * @return WmsCreateOrderReq
     */
    default WmsPurchaseOrderReq initWmsCreateOrderRequest(TzOrderPurchase purchaseOrder,
      TzOrderSupplier supplierOrder, List<TzOrderItem> orderItems) {

        return WmsPurchaseOrderReq.builder()
          // 采购单号
          .purchaseNo(purchaseOrder.getPurchaseOrderNo())
          // 供应商单号
          .shopOrderId(supplierOrder.getSupplierOrderNo())
          // 1688 订单 id
          .orderId(supplierOrder.getPlatformOrderId() != null ? Long.parseLong(supplierOrder.getPlatformOrderId()) : 0L)
          // 卖家 id
          .sellerOpenId(supplierOrder.getSupplierId())
          // 采购订单状态
          .status(WmsOrderStatusEnum.PENDING_PAYMENT)
          // 必填 通过仓库 ID 获取，默认值 536 ，代表 惠州仓
          // TODO 2025年07月17日10:35:41 后期需要改掉
          .storeId("536")
          // 当前的用户 id
          .createUser(String.valueOf(purchaseOrder.getBuyerId()))
          .createTime(supplierOrder.getGmtCreated())
          .platform(supplierOrder.getPlatformCode().name())
          // 必填
          .payType(WmsPayTypeEnum.BALANCE)
          // 1688 外部交易流水号
          .outTradeNo("")
          // 支付的链接
          .payUrl("")
          // 是否询价单
          .isRequestQuote(false)
          // 支付时间
          .paymentTime(supplierOrder.getPaymentDate() != null
            ? supplierOrder.getPaymentDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 发货时间
          .shippingTime(supplierOrder.getShippedDate() != null
            ? supplierOrder.getShippedDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 完成时间
          .completeTime(supplierOrder.getCompletedDate() != null
            ? supplierOrder.getCompletedDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 商品链接
          .link(null)
          // 物流单号
          .trackingNo("")
          // 服务费
          .serviceFee(supplierOrder.getServiceFee())
          // 商品金额 + 运费
          .total(supplierOrder.getCustomerGoodsAmount().add(supplierOrder.getCustomerFreightAmount()))
          // 运费
          .shippingFee(supplierOrder.getCustomerFreightAmount())
          // 原始总价
          .originalTotalPrice(supplierOrder.getPayableAmountTotal())
          // 原始运费
          .originalShippingFee(supplierOrder.getPayableFreightAmount())
          // 商品原始总金额
          .productOriginalTotalAmount(supplierOrder.getPayableGoodsAmount())
          // 商品销售总金额
          .salesTotalAmount(supplierOrder.getCustomerGoodsAmount())
          // 折扣优惠
          .discount(supplierOrder.getPayableDiscountAmount())
          // plus 折扣
          .plusDiscount(supplierOrder.getPayablePlusDiscountAmount())
          // 优惠券折扣
          .couponDiscount(supplierOrder.getPayableCouponAmount())
          // 总金额
          .totalAmount(supplierOrder.getCustomerTotalAmount())
          // 商品最终总金额
          .productFinalTotalAmount(BigDecimal.ZERO)
          // 最终运费
          .finalShoppingFee(supplierOrder.getPayableFreightAmount())
          // 平台状态
          .platformStatus(supplierOrder.getStatus().name())
          // 平台备注
          .platformRemark(supplierOrder.getStatus().getDescription())
          // 备注
          .remark(supplierOrder.getSupplierNotes())
          // 订单详情的列表
          .orderDetails(toWmsPurchaseOrderDetailsRes(orderItems))
          .build();
    }

    /**
     * 将TzOrderItem转换为WmsPurchaseOrderDetailsRes
     *
     * @param orderItems 订单商品列表
     * @return 转换成 wms 采购订单详情列表
     */
    default List<WmsPurchaseOrderDetailsReq> toWmsPurchaseOrderDetailsRes(List<TzOrderItem> orderItems) {
        List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetailsResList = Lists
          .newArrayListWithCapacity(orderItems.size());
        orderItems.forEach(item -> {
            // 商品规格
            String attrs = item.getSkuSpecs().stream().map(attr -> attr.getAttrKey() + ":" + attr.getAttrValue())
              .reduce((a, b) -> a + "," + b).orElse("");
            // 商品规格英文
            String attrsEn = item.getSkuSpecs().stream().map(attr -> attr.getAttrKeyTrans() + ":"
              + attr.getAttrValueTrans()).reduce((a, b) -> a + "," + b).orElse("");
            // 构建采购订单详情
            wmsPurchaseOrderDetailsResList.add(WmsPurchaseOrderDetailsReq.builder()
              .cnName(item.getProductTitle())
              .enName(item.getProductTitleEn())
              .skuId(item.getProductSkuId().toString())
              .productId(item.getProductSpuId().toString())
              .quantity(item.getQuantity().intValue())
              .imageUrl(item.getProductImageUrl())
              .unitPrice(item.getPrice())
              .skuAttrib(attrs)
              .skuAttribEn(attrsEn)
              .variantId(item.getPlatformSpecId())
              .originUnitPrice(item.getPrice())
              .finalUnitPrice(item.getPrice())
              .subTotal(item.getTotalAmount())
              .originSubTotalAmount(item.getTotalAmount())
              .finalSubTotalAmount(item.getTotalAmount())
              .weight(BigDecimal.ZERO)
              .build());
        });
        return wmsPurchaseOrderDetailsResList;
    }

    /**
     * 将供应商的采购单转换成 wms 采购订单详情请求
     *
     * @param purchaseOrder 采购单
     * @param supplierOrder 供应商采购单
     * @param orderItems    订单商品列表
     * @return WmsPurchaseOrderDetailReq
     */
    default WmsPurchaseOrderDetailReq toWmsPurchaseOrderDetailReq(TzOrderPurchase purchaseOrder, TzOrderSupplier supplierOrder, List<TzOrderItem> orderItems) {
        return WmsPurchaseOrderDetailReq.builder()
          // 采购单号
          .purchaseNo(purchaseOrder.getPurchaseOrderNo())
          // 供应商单号
          .shopOrderId(supplierOrder.getSupplierOrderNo())
          // 1688 订单 id
          .orderId(supplierOrder.getPlatformOrderId() != null ? Long.parseLong(supplierOrder.getPlatformOrderId()) : 0L)
          // 卖家 id
          .sellerOpenId(supplierOrder.getSupplierId())
          // 采购订单状态
          .status(WmsOrderStatusEnum.PENDING_PAYMENT)
          // 必填 通过仓库 ID 获取，默认值 536 ，代表 惠州仓
          // TODO 2025年07月17日10:35:41 后期需要改掉
          .storeId("536")
          // 当前的用户 id
          .createUser(String.valueOf(purchaseOrder.getBuyerId()))
          .createTime(supplierOrder.getGmtCreated())
          .platform(supplierOrder.getPlatformCode().name())
          // 必填
          .payType(WmsPayTypeEnum.BALANCE)
          // 1688 外部交易流水号
          .outTradeNo(supplierOrder.getPlatformTradeNo())
          // 支付的链接
          .payUrl("")
          // 是否询价单
          .isRequestQuote(false)
          // 支付时间
          .paymentTime(supplierOrder.getPaymentDate() != null
            ? supplierOrder.getPaymentDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 发货时间
          .shippingTime(supplierOrder.getShippedDate() != null
            ? supplierOrder.getShippedDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 完成时间
          .completeTime(supplierOrder.getCompletedDate() != null
            ? supplierOrder.getCompletedDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 商品链接
          .link(null)
          // 物流单号
          .trackingNo("")
          // 服务费
          .serviceFee(supplierOrder.getServiceFee())
          // 商品金额 + 运费
          .total(supplierOrder.getCustomerGoodsAmount().add(supplierOrder.getCustomerFreightAmount()))
          // 运费
          .shippingFee(supplierOrder.getCustomerFreightAmount())
          // 原始总价
          .originalTotalPrice(supplierOrder.getPayableAmountTotal())
          // 原始运费
          .originalShippingFee(supplierOrder.getPayableFreightAmount())
          // 商品原始总金额
          .productOriginalTotalAmount(supplierOrder.getPayableGoodsAmount())
          // 商品销售总金额
          .productSalesTotalAmount(supplierOrder.getCustomerGoodsAmount())
          // 折扣优惠
          .discount(supplierOrder.getPayableDiscountAmount())
          // plus 折扣
          .plusDiscount(supplierOrder.getPayablePlusDiscountAmount())
          // 优惠券折扣
          .couponDiscount(supplierOrder.getPayableCouponAmount())
          // 总金额
          .totalAmount(supplierOrder.getCustomerTotalAmount())
          // 商品最终总金额
          .productFinalTotalAmount(BigDecimal.ZERO)
          // 最终运费
          .finalShoppingFee(supplierOrder.getPayableFreightAmount())
          // 平台状态
          .platformStatus(supplierOrder.getStatus().name())
          // 平台备注
          .platformRemark(supplierOrder.getStatus().getDescription())
          // 备注
          .remark(supplierOrder.getSupplierNotes())
          // 订单详情的列表
          .orderDetails(toWmsPurchaseOrderDetailsRes(orderItems))
          .build();
    }
}
