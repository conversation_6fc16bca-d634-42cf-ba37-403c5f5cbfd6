/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 买家类型枚举
 *
 * <AUTHOR>
 * @date 2025/7/28 18:42
 * @description: todo
 * @since 1.0.0
 */
@Getter
public enum TzOrderPurchaseBuyerTypeEnums {

    /**
     * 默认系统买家
     */
    DEFAULT(0, "默认系统买家"),
    /**
     * wms 系统用户
     */
    WMS(1, "wms 系统用户"),
    ;

    @JsonValue
    @EnumValue
    private final Integer value;
    private final String description;

    TzOrderPurchaseBuyerTypeEnums(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
}
