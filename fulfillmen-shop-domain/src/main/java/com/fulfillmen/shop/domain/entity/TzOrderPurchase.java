/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 采购订单表-统一管理跨供应商采购
 *
 * <AUTHOR>
 * @date 2025/7/28 14:26
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tz_order_purchase")
public class TzOrderPurchase implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 采购订单编号
     */
    @TableField(value = "purchase_order_no")
    private String purchaseOrderNo;

    /**
     * 买家 ID
     */
    @TableField(value = "buyer_id")
    private Long buyerId;

    /**
     * 买家类型： =0 默认系统买家 可以通过 tzuser 获取用户。=1 wms 系统用户，使用的是 cusCode 标识。
     */
    @TableField(value = "buyer_type")
    private Integer buyerType;

    /**
     * 订单状态: 0待支付/1已支付/2采购中/3待发货/4部分发货/5已发货(所有商品都发货)/6送达仓库(等待入库-质检入库)/7已完成(所有订单已完成)/8已取消
     */
    @TableField(value = "order_status")
    private TzOrderPurchaseStatusEnum orderStatus;

    /**
     * 下单日期
     */
    @TableField(value = "order_date")
    private LocalDateTime orderDate;

    /**
     * 支付完成日期
     */
    @TableField(value = "paid_date")
    private LocalDateTime paidDate;

    /**
     * 付费流水号。
     */
    @TableField(value = "paid_transaction_no")
    private String paidTransactionNo;

    /**
     * 订单完成日期
     */
    @TableField(value = "order_completed_date")
    private LocalDateTime orderCompletedDate;

    /**
     * 服务费，整单的服务费价格。通过 tenant_info.service_fee 获取比例。wms 客户需要获取对应的服务费率
     */
    @TableField(value = "service_fee")
    private BigDecimal serviceFee;

    /**
     * 当前交易汇率快照
     */
    @TableField(value = "exchange_rate_snapshot")
    private BigDecimal exchangeRateSnapshot;

    /**
     * 客户支付的商品总金额
     */
    @TableField(value = "customer_goods_amount")
    private BigDecimal customerGoodsAmount;

    /**
     * 客户需要支付的运费
     */
    @TableField(value = "customer_total_freight")
    private BigDecimal customerTotalFreight;

    /**
     * 订单总金额
     */
    @TableField(value = "customer_total_amount")
    private BigDecimal customerTotalAmount;

    /**
     * 红包或优惠卷金额，通过供应商订单计算 tzordersupplier.payable_coupon_amount
     */
    @TableField(value = "payable_coupon_amount")
    private BigDecimal payableCouponAmount;

    /**
     * 原订单折扣金额 - 通过供应商订单计算 tzordersupplier.payable_discount_amount
     */
    @TableField(value = "payable_discount_amount")
    private BigDecimal payableDiscountAmount;

    /**
     * 应付商品金额 通过供应商订单计算 tzordersupplier.payable_goods_amount
     */
    @TableField(value = "payable_goods_amount")
    private BigDecimal payableGoodsAmount;

    /**
     * 应付总运费 通过供应商订单计算 tzordersupplier.payable_freight_total
     */
    @TableField(value = "payable_freight_total")
    private BigDecimal payableFreightTotal;

    /**
     * 应付总费用 通过供应商订单计算 tzordersupplier.payable_amount_total
     */
    @TableField(value = "payable_amount_total")
    private BigDecimal payableAmountTotal;

    /**
     * 实付金额 - 计算 供应商的订单实付金额 tzordersupplier.actual_payment_amount
     */
    @TableField(value = "actual_payment_amount")
    private BigDecimal actualPaymentAmount;

    /**
     * 实付商品总金额 - 通过计算所有供应商订单 实付商品总金额 tzordersupplier.actual_payment_goods_amount
     */
    @TableField(value = "actual_payment_goods_amount")
    private BigDecimal actualPaymentGoodsAmount;

    /**
     * 实付总运费 - 通过 供应商订单运费 实付运费 tzordersupplier.actual_payment_freight_amount
     */
    @TableField(value = "actual_payment_freight_amount")
    private BigDecimal actualPaymentFreightAmount;

    /**
     * 订单实际使用的优惠的金额。通过计算 供应商的订单优惠金额 tzordersupplier.actual_payment_discount_amount
     */
    @TableField(value = "actual_payment_discount_amount")
    private BigDecimal actualPaymentDiscountAmount;

    /**
     * 订单实际使用红包或优惠卷金额。通过计算支付成功后的 供应商的订单 tzordersupplier.actual_payment_coupon_amount
     */
    @TableField(value = "actual_payment_coupon_amount")
    private BigDecimal actualPaymentCouponAmount;

    /**
     * 订单实际plus 会员优惠金额。通过计算支付成功后的 供应商的订单 tzordersupplier.actual_payment_plus_amount
     */
    @TableField(value = "actual_payment_plus_amount")
    private BigDecimal actualPaymentPlusAmount;

    /**
     * 收货的仓库id
     */
    @TableField(value = "recipient_warehouse_id")
    private Long recipientWarehouseId;

    /**
     * 收货的仓库名称, tenant_warehouse
     */
    @TableField(value = "recipient_warehouse_name")
    private String recipientWarehouseName;

    /**
     * 收货地址详情
     */
    @Deprecated
    @TableField(value = "delivery_address")
    private String deliveryAddress;

    /**
     * 邮政编码
     */
    @Deprecated
    @TableField(value = "postal_code")
    private String postalCode;

    /**
     * 国家代码
     */
    @Deprecated
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 省份
     */
    @Deprecated
    @TableField(value = "province")
    private String province;

    /**
     * 城市
     */
    @Deprecated
    @TableField(value = "city")
    private String city;

    /**
     * 区县
     */
    @Deprecated
    @TableField(value = "district")
    private String district;

    /**
     * 收货人
     */
    @Deprecated
    @TableField(value = "consignee_name")
    private String consigneeName;

    /**
     * 收货人电话
     */
    @Deprecated
    @TableField(value = "consignee_phone")
    private String consigneePhone;

    /**
     * 供应商数量
     */
    @TableField(value = "supplier_count")
    private Integer supplierCount;

    /**
     * 订单行数
     */
    @TableField(value = "line_item_count")
    private Integer lineItemCount;

    /**
     * 已完成的供应商订单数量
     */
    @TableField(value = "completed_supplier_count")
    private Integer completedSupplierCount;

    /**
     * 商品总数量
     */
    @TableField(value = "total_quantity")
    private Integer totalQuantity;

    /**
     * 采购备注
     */
    @TableField(value = "purchase_notes")
    private String purchaseNotes;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    /**
     * 数据版本
     */
    @Version
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;
}
