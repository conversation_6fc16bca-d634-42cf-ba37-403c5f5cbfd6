/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;

/**
 * 订单项物流状态枚举
 *
 * <AUTHOR>
 * @date 2025/7/25 14:36
 * @description: 订单项物流状态
 * @since 1.0.0
 */
@Getter
public enum TzOrderItemLogisticsStatusEnum {

    /**
     * 未发货
     */
    NOT_SHIPPED(1, "未发货"),
    /**
     * 已发货
     */
    SHIPPED(2, "已发货"),
    /**
     * 已收货
     */
    RECEIVED(3, "已收货"),
    /**
     * 已经退货
     */
    RETURNED(4, "已经退货"),
    /**
     * 部分发货
     */
    PARTIALLY_SHIPPED(5, "部分发货"),
    /**
     * 还未创建物流订单
     */
    NOT_CREATED(8, "还未创建物流订单");

    @EnumValue
    @JsonValue
    private final int code;
    private final String desc;

    TzOrderItemLogisticsStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据代码获取枚举
     */
    public static TzOrderItemLogisticsStatusEnum getByCode(int code) {
        for (TzOrderItemLogisticsStatusEnum status : TzOrderItemLogisticsStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

}
