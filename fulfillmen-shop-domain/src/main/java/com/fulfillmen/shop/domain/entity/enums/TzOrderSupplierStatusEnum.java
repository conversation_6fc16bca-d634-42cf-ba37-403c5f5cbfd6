/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 供应商订单履约状态枚举
 *
 * <pre>
 * 供应商订单状态流转：
 *
 * 0. 待支付
 * 财务确认支付前，订单状态变为待支付
 *
 * 1. 待发货
 * 供应商确认订单，准备发货。
 * 对应 1688 waitsellersend
 *
 * 2. 部分发货
 * 供应商部分商品已发货，还有商品待发货。
 * 当前供应商的商品，存在部分发货
 *
 * 3. 待收货
 * 供应商所有商品都已发货。
 * 对应 1688 waitbuyerreceive
 *
 * 4. 已送达仓库
 * 货物已送达WMS仓库，等待质检入库。
 * 对应 1688 confirm_goods 已收货
 *
 * 5. 待入库
 * 所有商品货物已到达仓库，供应商订单履约完成。
 * 对应 wms 的入库单处理状态。
 *
 * 6. 部分商品，已质检入库
 * 部分商品已质检入库，还有商品待入库。
 * 对应 wms 的入库单处理状态。 如果存在 多笔入库单。 一笔一笔完成入库单。就是当前的状态
 *
 * 7. 国内 - 订单已完成
 * 货物已完成质检并入库，供应商订单履约完成。
 * wms 已完成当前供应商的所有商品质检并入库
 *
 * 8. 已取消
 * 订单被取消，需要处理退款等后续事宜。
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/24
 * @description 供应商订单履约状态枚举
 * @since 1.0.0
 */
@Getter
public enum TzOrderSupplierStatusEnum {

    /**
     * 待支付
     *
     * <pre>
     * 财务确认支付前，订单状态变为待支付。
     * 对应 1688 waitbuyerpay
     * </pre>
     */
    PENDING_PAYMENT(0, "待支付"),

    /**
     * 待发货
     *
     * <pre>
     * 供应商确认订单，准备发货。
     * 对应 1688 waitsellersend
     * </pre>
     */
    PENDING_SHIPMENT(1, "待发货"),

    /**
     * 部分发货
     *
     * <pre>
     * 供应商部分商品已发货，还有商品待发货。
     * 当前供应商的商品，存在部分发货
     * </pre>
     */
    PARTIALLY_SHIPPED(2, "部分发货"),

    /**
     * 待收货
     *
     * <pre>
     * 供应商所有商品都已发货。
     * 对应 1688 waitbuyerreceive
     * </pre>
     */
    SHIPPED(3, "待收货"),

    /**
     * 已送达仓库
     *
     * <pre>
     * 货物已送达WMS仓库，等待质检入库。
     * 对应 1688 confirm_goods 已收货，待确认
     * </pre>
     */
    WAREHOUSE_PENDING_RECEIPT(4, "等待仓库签收"),

    /**
     * 仓库已签收
     *
     * <pre>
     * 所有商品货物已到达仓库，供应商订单履约完成。
     * 对应 wms 的入库单处理状态。 等待 wms 入库订单处理状态
     * </pre>
     */
    WAREHOUSE_RECEIVED(5, "仓库签收"),

    /**
     * 国内 - 订单已完成
     *
     * <pre>
     * 货物已完成质检并入库，供应商订单履约完成。
     * wms 已完成当前供应商的所有商品质检并入库
     * </pre>
     */
    COMPLETED(6, "已完成"),

    /**
     * 已取消
     *
     * <pre>
     * 订单被取消，需要处理退款等后续事宜。
     * </pre>
     */
    CANCELLED(7, "已取消")

    ;

    @EnumValue
    @JsonValue
    private final Integer value;

    private final String description;

    TzOrderSupplierStatusEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    @JsonCreator
    public static TzOrderSupplierStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TzOrderSupplierStatusEnum status : TzOrderSupplierStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     */
    public static TzOrderSupplierStatusEnum getByDescription(String description) {
        if (description == null) {
            return null;
        }
        for (TzOrderSupplierStatusEnum status : TzOrderSupplierStatusEnum.values()) {
            if (status.getDescription().equals(description)) {
                return status;
            }
        }
        return null;
    }
}
