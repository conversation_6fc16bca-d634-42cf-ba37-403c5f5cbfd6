/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * 采购订单状态枚举
 *
 * <pre>
 * 采购订单完整状态流转：
 *
 * 0. 临时保存 (Temporarily Saved)
 * - 询价商品需要代付款，订单保存等待进一步处理
 * - 客户余额不足，订单保存等待充值
 * - 数据会同步到 WMS 或管理后台进行处理
 *
 * 1. 待支付 (Payment Pending)
 * - 订单已确认商品和价格，等待客户完成支付
 * - 可选支付方式：余额支付、Stripe支付、Alipay 等
 *
 * 2. 支付完成 (Payment Completed)
 * - 客户支付成功，余额已扣减或第三方支付完成
 * - 订单进入内部处理流程，等待采购员进行下单处理
 *
 * 3. 采购中 (Procurement In Progress)
 * - 采购员已开始处理订单，审核用户支付账单
 *
 * 4. 部分履约 (Partially Fulfilled)
 * - 部分商品已开始采购，还有商品待采购
 * - 客户可以看到部分商品的进展
 *
 * 5. 采购完成 (Procurement Completed)
 * - 所有商品都已开始采购，等待供应商发货
 * - 可能存在多个供应商分别发货的情况
 *
 * 6. 供应商已发货 (Supplier Shipped)
 * - 供应商已发货，货物在等待快递公司收件
 *
 * 7. 仓库待收货 (Warehouse Pending Received)
 * - 快递公司已揽收。
 * - 货物在运输途中，等待 WMS 仓库接收
 * - 所有供应商都已发货，但仓库尚未完全接收
 *
 * 8. 仓库已收货 (Warehouse Received)
 * - WMS 仓库确认收到所有货物
 * - 货物进入质检和上架流程
 *
 * 9. 已入库 (In Stock)
 * - 货物质检完成并成功上架
 * - 可以开始准备发货给最终客户
 *
 * 10. 已取消 (Cancelled)
 * - 订单在完成前被取消
 * - 可能发生在支付前、采购前或发货前
 * - 需要根据取消时点处理退款等后续事宜
 *
 * 11. 代付款 (Payment Pending For Partial)
 * - 订单中包含代付款商品，等待客户完成支付
 *
 * 12. 已完成 (Order Completed)
 * - 订单所有商品都已完成采购和发货
 *
 * 13. 退款中 (Order Refunding)
 * - 订单退款中
 * - wms 订单取消，1688 订单申请退款。 并更新此状态
 *
 * 14. 已退款 (Order Refunded)
 * - 订单已退款
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/27
 * @description 采购订单状态枚举
 * @since 1.0.0
 */
@Getter
public enum TzOrderPurchaseStatusEnum {

    /**
     * 临时保存，wms 将创建订单
     *
     * <pre>
     * 订单临时保存状态，适用于以下情况：
     * - 询价商品需要代付款，订单保存等待进一步处理
     * - 客户余额不足，订单保存等待充值
     * - 数据会同步到 WMS 或管理后台进行处理
     * </pre>
     */
    TEMPORARILY_SAVED(0, "临时保存", "Temporarily Save Orders"),

    /**
     * 用户待支付，wms 将创建订单
     *
     * <pre>
     * 订单已确认商品和价格，等待客户完成支付
     * 可选支付方式：余额支付、Stripe支付、Alipay 等
     * </pre>
     * 发起支付后，可以进入 PAYMENT_COMPLETED 或 ORDER_CANCELLED
     */
    PAYMENT_PENDING(1, "待支付", "Payment Pending"),

    /**
     * 用户支付完成
     *
     * <pre>
     * 客户支付成功，余额已扣减或第三方支付完成
     * 订单进入内部处理流程，等待采购员进行下单处理
     * </pre>
     * 支付完成后，可以进入 PENDING_REVIEW 或 ORDER_CANCELLED
     */
    PAYMENT_COMPLETED(2, "支付完成", "Payment Completed"),

    /**
     * 待审核
     *
     * <pre>
     * 采购员/财务 审核用户支付账单.
     * </pre>
     * 审核账单后，可以进入 PROCUREMENT_IN_PROGRESS 或 ORDER_CANCELLED
     */
    PENDING_REVIEW(3, "待审核", "Pending Review"),

    /**
     * 采购中
     *
     * <pre>
     * 采购员已开始处理订单
     * </pre>
     * 采购员审核通过后，将发起采购流程，支付供应商货款 PARTIALLY_PROCUREMENT 或 PROCUREMENT_COMPLETED 、ORDER_CANCELLED
     */
    PROCUREMENT_IN_PROGRESS(4, "采购中", "Procurement In Progress"),

    /**
     * 部分采购完成(部分供应商的订单状态显示已支付)
     *
     * <pre>
     * 部分供应商订单已完成，但还有供应商订单在处理中
     * 客户可以看到部分商品的进展
     * </pre>
     */
    PARTIALLY_PROCUREMENT(5, "部分履约", "Partially Fulfilled"),

    /**
     * 采购完成
     *
     * <pre>
     * 所有供应商订单都已完成采购(已支付), 状态都变成了 等待发货
     *
     * 平台完成采购，供应商已将货物发往 WMS 仓库。
     * 可能存在多个供应商分别发货的情况
     * 采购单下的所有订单
     * {@see TzTzOrderItemLogisticsStatusEnum.NOT_SHIPPED}
     * 或 {@see TzTzOrderItemLogisticsStatusEnum.NOT_CREATED}
     * 或 {@see TzTzOrderItemLogisticsStatusEnum.PARTIALLY_SHIPPED}
     * 统一显示 待发货
     * </pre>
     */
    PROCUREMENT_COMPLETED(6, "采购完成，等待供应商发货", "Procurement Completed，Waiting for Supplier Shipment"),

    /**
     * 供应商已发货
     *
     * <pre>
     * 供应商已发货，货物在等待快递公司收件
     * 采购单下的所有订单项显示 {@see TzTzOrderItemLogisticsStatusEnum.SHIPPED} 已发货
     * </pre>
     */
    SUPPLIER_SHIPPED(7, "供应商已发货", "Supplier Shipped"),

    /**
     * 仓库待收货
     *
     * <pre>
     * 快递公司已揽收。
     * 货物在运输途中，等待 WMS 仓库接收
     * 所有供应商都已发货，但仓库尚未完全接收
     * 采购单下的所有订单 {@see TzTzOrderItemLogisticsStatusEnum.PENDING_RECEIPT} 待入库
     * </pre>
     */
    WAREHOUSE_PENDING_RECEIVED(8, "仓库待收货", "Warehouse Pending Received"),

    /**
     * 仓库已收货
     *
     * <pre>
     * WMS 仓库确认收到所有货物
     * 货物进入质检和上架流程
     * 采购单下的所有订单 {@see TzTzOrderItemLogisticsStatusEnum.RECEIVED} 已入库
     * </pre>
     */
    WAREHOUSE_RECEIVED(9, "仓库已收货", "Warehouse Received"),

    /**
     * 已入库
     *
     * <pre>
     * 货物质检完成并成功上架
     * 可以开始准备发货给最终客户
     * 这是采购订单的最终完成状态
     * </pre>
     */
    IN_STOCK(10, "已入库", "In Stock"),

    /**
     * 已取消
     *
     * <pre>
     * 订单在完成前被取消
     * 可能发生在支付前、采购前或发货前
     * 需要根据取消时点处理退款
     * </pre>
     */
    ORDER_CANCELLED(11, "已取消", "Order Cancelled"),

    /**
     * 代付款
     *
     * <pre>
     * 订单中包含代付款商品，等待客户完成支付
     * </pre>
     */
    PAYMENT_PENDING_FOR_PARTIAL(12, "代付款", "Payment Pending For Partial"),

    /**
     * 订单完成
     *
     * <pre>
     * 订单所有商品都已完成采购和发货
     * </pre>
     */
    ORDER_COMPLETED(13, "已完成", "Order Completed"),
    /**
     * 订单退款中
     *
     * <pre>
     * 订单退款中
     * </pre>
     */
    ORDER_REFUNDING(14, "退款中", "Order Refunding"),
    /**
     * 订单已退款
     *
     * <pre>
     * 订单已退款
     * </pre>
     */
    ORDER_REFUNDED(15, "已退款", "Order Refunded");

    @EnumValue
    @JsonValue
    private final Integer value;

    /**
     * 中文描述
     */
    private final String description;

    /**
     * 英文描述
     */
    private final String englishDescription;

    TzOrderPurchaseStatusEnum(Integer value, String description, String englishDescription) {
        this.value = value;
        this.description = description;
        this.englishDescription = englishDescription;
    }

    @JsonCreator
    public static TzOrderPurchaseStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TzOrderPurchaseStatusEnum status : TzOrderPurchaseStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据中文描述获取枚举
     */
    public static TzOrderPurchaseStatusEnum getByDescription(String description) {
        if (description == null) {
            return null;
        }
        return Arrays.stream(TzOrderPurchaseStatusEnum.values()).filter(status -> Objects.equals(status.getDescription(), description)).findFirst().orElse(null);
    }

    /**
     * 根据英文描述获取枚举
     */
    public static TzOrderPurchaseStatusEnum getByEnglishDescription(String englishDescription) {
        return Arrays.stream(TzOrderPurchaseStatusEnum.values()).filter(Objects::nonNull).filter(status -> Objects.equals(status.getEnglishDescription(), englishDescription))
            .findFirst().orElse(null);
    }

    /**
     * 判断是否为可支付状态
     */
    public boolean isPayable() {
        return this == TEMPORARILY_SAVED || this == PAYMENT_PENDING;
    }

    /**
     * 判断是否为可取消状态
     */
    public boolean isCancellable() {
        return this != IN_STOCK && this != ORDER_CANCELLED;
    }

    /**
     * 判断是否为已完成状态
     */
    public boolean isCompleted() {
        return this == IN_STOCK;
    }

    /**
     * 判断是否为进行中状态
     */
    public boolean isInProgress() {
        return this == PAYMENT_COMPLETED ||
            this == PENDING_REVIEW ||
            this == PARTIALLY_PROCUREMENT ||
            this == PROCUREMENT_IN_PROGRESS ||
            this == SUPPLIER_SHIPPED ||
            this == WAREHOUSE_PENDING_RECEIVED;
    }
}
