/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 排序方式枚举
 *
 * <AUTHOR>
 * @date 2025/3/1
 * @description: 排序方式枚举
 * @since 1.0.0
 */
@Getter
@RequiredArgsConstructor
@Schema(description = "排序方式枚举")
public enum SortOrderEnum {

    /**
     * 升序
     */
    ASC("asc", "升序"),

    /**
     * 降序
     */
    DESC("desc", "降序");

    /**
     * 排序方式代码
     */
    private final String code;

    /**
     * 排序方式描述
     */
    private final String description;

    /**
     * 获取JSON值
     *
     * @return JSON值
     */
    @JsonValue
    public String getCode() {
        return code;
    }

    /**
     * 从代码创建枚举
     *
     * @param code 代码
     * @return 枚举值，如果不存在则返回null
     */
    @JsonCreator
    public static SortOrderEnum fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (SortOrderEnum sortOrderEnum : SortOrderEnum.values()) {
            if (sortOrderEnum.getCode().equals(code)) {
                return sortOrderEnum;
            }
        }
        return null;
    }

    /**
     * 验证排序方式是否有效
     *
     * @param code 排序方式代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return fromCode(code) != null;
    }

    /**
     * 获取所有有效的排序方式代码
     *
     * @return 所有有效的排序方式代码
     */
    public static String[] getCodes() {
        SortOrderEnum[] values = SortOrderEnum.values();
        String[] codes = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            codes[i] = values[i].getCode();
        }
        return codes;
    }
}