/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzProductSpuSingleItemEnum;
import com.fulfillmen.shop.domain.entity.json.AttrJson;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单项表-具体商品采购明细
 *
 * <AUTHOR>
 * @date 2025/7/28 14:26
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tz_order_item", autoResultMap = true)
public class TzOrderItem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 采购订单ID
     */
    @TableField(value = "purchase_order_id")
    private Long purchaseOrderId;

    /**
     * 供应商订单ID
     */
    @TableField(value = "supplier_order_id")
    private Long supplierOrderId;

    /**
     * 平台订单ID 对应 1688 id
     */
    @TableField(value = "platform_order_id")
    private String platformOrderId;

    /**
     * 行号
     */
    @TableField(value = "line_number")
    private Integer lineNumber;

    /**
     * 商品SPU ID
     */
    @TableField(value = "product_spu_id")
    private Long productSpuId;

    /**
     * 商品SKU ID
     */
    @TableField(value = "product_sku_id")
    private Long productSkuId;

    /**
     * 平台商品ID
     */
    @TableField(value = "platform_product_id")
    private String platformProductId;

    /**
     * 平台SKU ID
     */
    @TableField(value = "platform_sku_id")
    private String platformSkuId;

    /**
     * 平台 specId
     */
    @TableField(value = "platform_spec_id")
    private String platformSpecId;

    /**
     * 订单保留的商品快照连接
     */
    @TableField(value = "platform_snapshot_url")
    private String platformSnapshotUrl;

    /**
     * 当前订单的元数据
     */
    @TableField(value = "platform_metadata")
    private String platformMetadata;

    /**
     * 商品标题
     */
    @TableField(value = "product_title")
    private String productTitle;

    /**
     * 商品英文标题
     */
    @TableField(value = "product_title_en")
    private String productTitleEn;

    /**
     * 商品链接
     */
    @TableField(value = "product_link")
    private String productLink;

    /**
     * SKU规格属性
     */
    @TableField(value = "sku_specs", typeHandler = JacksonTypeHandler.class)
    private List<AttrJson> skuSpecs;

    /**
     * 商品图片
     */
    @TableField(value = "product_image_url")
    private String productImageUrl;

    /**
     * 单价
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 订购数量
     */
    @TableField(value = "quantity")
    private BigDecimal quantity;

    /**
     * 总金额 = price * qty
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 实付金额
     */
    @TableField(value = "actual_payment_amount")
    private BigDecimal actualPaymentAmount;

    /**
     * 计量单位
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 计量单位(英文)
     */
    @TableField(value = "unit_en")
    private String unitEn;

    /**
     * 状态：0 待付款、1 已付款、2 采购中、3 待发货、4 待收货、5 待签收、6 已签收(待入库)、7 已入库、8 已完成 、9 退货退款、-1 已取消
     */
    @TableField(value = "`status`")
    private TzOrderItemStatusEnum status;

    /**
     * 对应 1688 物流状态：1 未发货 2 已发货 3 已收货 4 已经退货 5 部分发货 8 还未创建物流订单
     */
    @TableField(value = "logistics_status")
    private TzOrderItemLogisticsStatusEnum logisticsStatus;

    /**
     * 下单的错误码
     */
    @TableField(value = "error_code")
    private Integer errorCode;

    /**
     * 下单的错误描述
     */
    @TableField(value = "error_message")
    private String errorMessage;

    /**
     * 外部平台订单行ID ，如果外部有返回 商品明细 id。
     */
    @TableField(value = "external_item_id")
    private String externalItemId;

    /**
     * 重量，按重量单位计算。
     */
    @TableField(value = "weight")
    private String weight;

    /**
     * 重量. 搭配重量单位
     */
    @TableField(value = "weight_unit")
    private String weightUnit;

    /**
     * 完成时间
     */
    @TableField(value = "completed_datetime")
    private LocalDateTime completedDatetime;

    /**
     * 是否单品，0 否，1 是。 如果是单品的，创建订单的时候只需要传 offerId ，不需要 specId
     */
    @TableField(value = "is_single_item")
    private TzProductSpuSingleItemEnum isSingleItem;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id")
    private Long tenantId;

    /**
     * 是否删除
     */
    @TableField(value = "is_deleted")
    private Long isDeleted;

    /**
     * 数据版本
     */
    @Version
    @TableField(value = "revision")
    private Integer revision;

    /**
     * 创建时间
     */
    @TableField(value = "gmt_created")
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    @TableField(value = "gmt_modified")
    private LocalDateTime gmtModified;
}
