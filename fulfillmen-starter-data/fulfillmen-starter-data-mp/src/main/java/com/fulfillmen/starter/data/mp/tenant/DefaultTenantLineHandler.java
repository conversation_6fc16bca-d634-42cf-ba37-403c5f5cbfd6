/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.data.mp.tenant;

import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;

import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;

/**
 * 默认多租户处理器，基于TenantContext获取租户ID。
 * 
 * <AUTHOR>
 * @since 2025-05-06
 */
public class DefaultTenantLineHandler implements TenantLineHandler {
    private final TenantContext tenantContext;

    @Autowired
    public DefaultTenantLineHandler(TenantContext tenantContext) {
        this.tenantContext = tenantContext;
    }

    @Override
    public Expression getTenantId() {
        String tenantId = tenantContext.getCurrentTenantId();
        return tenantId == null ? null : new StringValue(tenantId);
    }

    @Override
    public String getTenantIdColumn() {
        return "tenantId";
    }

    @Override
    public boolean ignoreTable(String tableName) {
        // 可扩展：可配置忽略表
        return false;
    }
}
