/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.data.mp.autoconfigure.idgenerator;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.fulfillmen.starter.data.mp.TestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

/**
 * MyBatis Plus ID 生成器配置测试
 *
 * <AUTHOR>
 * @date 2025/02/08 11:55
 */
@SpringBootTest(classes = TestApplication.class)
@ActiveProfiles("test")
class MyBatisPlusIdGeneratorConfigurationTest {

    @Autowired
    private IdentifierGenerator identifierGenerator;

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void testDefaultIdGenerator() {
        assertNotNull(identifierGenerator);
        // 测试生成的ID是否为Long类型
        Object id = identifierGenerator.nextId(null);
        System.out.println(id);
        assertTrue(id instanceof Long);
    }

    @Test
    void testCosIdGenerator() {
        // 由于未配置 CosId，应该抛出异常
        assertThrows(NoSuchBeanDefinitionException.class, () -> applicationContext
            .getBean(MyBatisPlusCosIdIdentifierGenerator.class));
    }
}