/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.storage.autoconfigure;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import com.fulfillmen.starter.storage.client.OssClient;
import com.fulfillmen.starter.storage.dao.StorageDao;
import com.fulfillmen.starter.storage.dao.impl.StorageDaoDefaultImpl;
import com.fulfillmen.starter.storage.factory.OssStorageStrategyProvider;
import com.fulfillmen.starter.storage.model.req.OssStorageProperties;
import com.fulfillmen.starter.storage.strategy.OssStorageStrategy;

/**
 * OSS 存储自动配置
 *
 * <AUTHOR>
 * @date 2024/6/20
 */
@AutoConfiguration
@EnableConfigurationProperties(OssStorageProperties.class)
@ConditionalOnProperty(prefix = "fulfillmen-starter.storage.oss", name = "enabled", havingValue = "true")
public class OssStorageAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(OssStorageAutoConfiguration.class);

    @Autowired
    private OssStorageProperties properties;

    /**
     * 验证并设置默认属性
     *
     * @param properties OSS存储属性
     */
    private void validateAndSetDefaultProperties(OssStorageProperties properties) {
        if (properties.getAccessKey() == null) {
            log.error("AccessKey不能为空");
            throw new IllegalArgumentException("AccessKey不能为空");
        }

        if (properties.getSecretKey() == null) {
            log.error("SecretKey不能为空");
            throw new IllegalArgumentException("SecretKey不能为空");
        }

        if (properties.getEndpoint() == null) {
            log.error("Endpoint不能为空");
            throw new IllegalArgumentException("Endpoint不能为空");
        }

        if (properties.getBucketName() == null) {
            log.error("BucketName不能为空");
            throw new IllegalArgumentException("BucketName不能为空");
        }

        if (properties.getIsDefault() == null) {
            log.info("Setting default isDefault: true");
            properties.setIsDefault(true);
        }

        if (properties.getEnabled() == null) {
            log.info("Setting default enabled: true");
            properties.setEnabled(true);
        }

        if (properties.getCode() == null) {
            log.info("Setting default code: OSS");
            properties.setCode("OSS");
        }

        if (properties.getDomain() == null) {
            properties.setDomain("");
        }

        if (properties.getRegion() == null) {
            properties.setRegion("");
        }

        log.debug("Validated properties: isDefault={}, enabled={}, endpoint={}, bucketName={}, code={}", properties
            .getIsDefault(), properties.getEnabled(), properties.getEndpoint(), properties.getBucketName(), properties
                .getCode());
    }

    /**
     * 存储DAO Bean
     *
     * @return 存储DAO
     */
    @Bean
    @ConditionalOnMissingBean
    public StorageDao storageDao() {
        log.info("Configuring StorageDao");
        validateAndSetDefaultProperties(properties);
        return new StorageDaoDefaultImpl();
    }

    /**
     * OSS客户端 Bean
     *
     * @return OSS客户端
     */
    @Bean
    @ConditionalOnMissingBean
    public OssClient ossClient() {
        log.info("Configuring OssClient");
        validateAndSetDefaultProperties(properties);
        return new OssClient(properties);
    }

    /**
     * OSS存储策略 Bean
     *
     * @param ossClient  OSS客户端
     * @param storageDao 存储DAO
     * @return OSS存储策略
     */
    @Bean
    @ConditionalOnMissingBean
    public OssStorageStrategy ossStorageStrategy(OssClient ossClient, StorageDao storageDao) {
        log.info("Configuring OssStorageStrategy");
        return new OssStorageStrategy(ossClient, storageDao);
    }

    /**
     * OSS存储策略提供者 Bean
     *
     * @param ossStorageStrategy OSS存储策略
     * @return OSS存储策略提供者
     */
    @Bean
    @ConditionalOnMissingBean
    public OssStorageStrategyProvider ossStorageStrategyProvider(OssStorageStrategy ossStorageStrategy) {
        log.info("Configuring OssStorageStrategyProvider");
        validateAndSetDefaultProperties(properties);
        return new OssStorageStrategyProvider(ossStorageStrategy, properties);
    }
}
