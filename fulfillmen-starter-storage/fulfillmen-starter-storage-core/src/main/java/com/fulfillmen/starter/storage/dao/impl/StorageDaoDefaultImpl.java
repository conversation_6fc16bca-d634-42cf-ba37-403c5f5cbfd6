/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.storage.dao.impl;

import com.fulfillmen.starter.storage.dao.StorageDao;
import com.fulfillmen.starter.storage.model.resp.UploadResp;

/**
 * 存储记录持久层接口默认实现
 * <p>此类并不能真正保存记录，只是用来脱离数据库运行，保证文件上传功能可以正常使用</p>
 *
 * <AUTHOR>
 * @date 2025/4/16 11:57
 */
public class StorageDaoDefaultImpl implements StorageDao {

    @Override
    public void add(UploadResp uploadResp) {
    }
}
