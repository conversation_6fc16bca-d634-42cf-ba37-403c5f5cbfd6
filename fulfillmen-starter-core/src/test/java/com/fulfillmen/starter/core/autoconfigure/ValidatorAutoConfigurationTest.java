/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.core.autoconfigure;

import com.fulfillmen.starter.core.test.BaseTest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.util.Set;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ValidatorAutoConfiguration 测试类
 *
 * <AUTHOR>
 * @date 2025/02/05 15:00
 * @description: ValidatorAutoConfiguration 的单元测试
 * @since 1.0.0
 */
@DisplayName("ValidatorAutoConfiguration 测试")
@SpringBootTest
@Import(ValidatorAutoConfiguration.class)
@TestPropertySource(properties = {"spring.messages.basename=messages", "spring.messages.encoding=UTF-8"})
class ValidatorAutoConfigurationTest extends BaseTest {

    @SpringBootConfiguration
    @EnableAutoConfiguration
    static class TestConfig {
        @Bean
        public MessageSource messageSource() {
            ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
            messageSource.setBasename("messages");
            messageSource.setDefaultEncoding("UTF-8");
            messageSource.setUseCodeAsDefaultMessage(true);
            return messageSource;
        }
    }

    @Autowired
    private LocalValidatorFactoryBean validator;

    static class TestBean {
        @NotBlank(message = "name cannot be blank")
        @Size(min = 2, max = 10, message = "name length must be between 2 and 10")
        private String name;

        @Min(value = 0, message = "age must be greater than or equal to 0")
        @Max(value = 150, message = "age must be less than or equal to 150")
        private Integer age;

        @Email(message = "email format is invalid")
        private String email;

        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "phone number format is invalid")
        private String phone;

        @NotNull(message = "gender cannot be null")
        private Gender gender;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getAge() {
            return age;
        }

        public void setAge(Integer age) {
            this.age = age;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public Gender getGender() {
            return gender;
        }

        public void setGender(Gender gender) {
            this.gender = gender;
        }
    }

    enum Gender {
        MALE, FEMALE
    }

    @Test
    @DisplayName("测试验证器配置")
    void testValidatorConfiguration() {
        assertNotNull(validator);
    }

    @Test
    @DisplayName("测试名称校验")
    void testNameValidation() {
        TestBean testBean = new TestBean();

        // 测试空值校验
        Set<ConstraintViolation<TestBean>> violations = validator.validate(testBean);
        assertFalse(violations.isEmpty(), "应该有验证错误");
        assertTrue(violations.stream()
            .map(ConstraintViolation::getPropertyPath)
            .map(Object::toString)
            .anyMatch(path -> path.equals("name")), "应该包含name字段的验证错误");

        // 测试长度校验 - 过短
        testBean.setName("a");
        violations = validator.validate(testBean);
        assertFalse(violations.isEmpty(), "应该有验证错误");
        assertTrue(violations.stream()
            .map(ConstraintViolation::getPropertyPath)
            .map(Object::toString)
            .anyMatch(path -> path.equals("name")), "应该包含name字段的验证错误");

        // 测试长度校验 - 过长
        testBean.setName("thisistoolongname");
        violations = validator.validate(testBean);
        assertFalse(violations.isEmpty(), "应该有验证错误");
        assertTrue(violations.stream()
            .map(ConstraintViolation::getPropertyPath)
            .map(Object::toString)
            .anyMatch(path -> path.equals("name")), "应该包含name字段的验证错误");

        // 测试正常值
        testBean.setName("test");
        violations = validator.validate(testBean);
        assertFalse(violations.stream()
            .map(ConstraintViolation::getPropertyPath)
            .map(Object::toString)
            .anyMatch(path -> path.equals("name")), "不应该包含name字段的验证错误");
    }

    @Test
    @DisplayName("测试年龄校验")
    void testAgeValidation() {
        TestBean testBean = new TestBean();
        testBean.setName("test"); // 设置有效名称

        // 测试负数
        testBean.setAge(-1);
        Set<ConstraintViolation<TestBean>> violations = validator.validate(testBean);
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("age must be greater than or equal to 0")));

        // 测试超过最大值
        testBean.setAge(151);
        violations = validator.validate(testBean);
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("age must be less than or equal to 150")));

        // 测试正常值
        testBean.setAge(25);
        violations = validator.validate(testBean);
        assertFalse(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("age")));
    }

    @Test
    @DisplayName("测试邮箱校验")
    void testEmailValidation() {
        TestBean testBean = new TestBean();
        testBean.setName("test"); // 设置有效名称

        // 测试无效邮箱格式
        testBean.setEmail("invalid-email");
        Set<ConstraintViolation<TestBean>> violations = validator.validate(testBean);
        assertFalse(violations.isEmpty(), "应该有验证错误");
        assertTrue(violations.stream()
            .map(ConstraintViolation::getPropertyPath)
            .map(Object::toString)
            .anyMatch(path -> path.equals("email")), "应该包含email字段的验证错误");

        // 测试正常值
        testBean.setEmail("<EMAIL>");
        violations = validator.validate(testBean);
        assertFalse(violations.stream()
            .map(ConstraintViolation::getPropertyPath)
            .map(Object::toString)
            .anyMatch(path -> path.equals("email")), "不应该包含email字段的验证错误");
    }

    @Test
    @DisplayName("测试手机号校验")
    void testPhoneValidation() {
        TestBean testBean = new TestBean();
        testBean.setName("test"); // 设置有效名称

        // 测试无效手机号格式
        testBean.setPhone("123456");
        Set<ConstraintViolation<TestBean>> violations = validator.validate(testBean);
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("phone number format is invalid")));

        // 测试正常值
        testBean.setPhone("13800138000");
        violations = validator.validate(testBean);
        assertFalse(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("phone")));
    }

    @Test
    @DisplayName("测试性别校验")
    void testGenderValidation() {
        TestBean testBean = new TestBean();
        testBean.setName("test"); // 设置有效名称

        // 测试空值
        Set<ConstraintViolation<TestBean>> violations = validator.validate(testBean);
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("gender cannot be null")));

        // 测试正常值
        testBean.setGender(Gender.MALE);
        violations = validator.validate(testBean);
        assertFalse(violations.stream().anyMatch(v -> v.getPropertyPath().toString().equals("gender")));
    }

    @Test
    @DisplayName("测试验证器行为")
    void testValidatorBehavior() {
        // 创建一个包含多个验证错误的对象
        TestEntity entity = new TestEntity();
        entity.setValue(-1); // 违反 @Min
        // 不设置name，违反 @NotNull

        // 验证对象
        Set<ConstraintViolation<TestEntity>> violations = validator.validate(entity);

        // 应该返回所有的验证错误
        assertEquals(2, violations.size(), "应该返回所有的验证错误");

        // 验证具体的错误
        assertTrue(violations.stream()
            .map(ConstraintViolation::getPropertyPath)
            .map(Object::toString)
            .anyMatch(path -> path.equals("name")), "应该包含name字段的验证错误");

        assertTrue(violations.stream()
            .map(ConstraintViolation::getPropertyPath)
            .map(Object::toString)
            .anyMatch(path -> path.equals("value")), "应该包含value字段的验证错误");
    }

    @Data
    static class TestEntity {
        @NotNull
        private String name;

        @Min(0)
        @Max(100)
        private int value;
    }
}