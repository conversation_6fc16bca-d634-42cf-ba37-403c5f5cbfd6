/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.core.validation.constraints;

import static org.junit.jupiter.api.Assertions.*;

import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * EnumValueValidator 测试类
 *
 * <AUTHOR>
 * @date 2025/02/05 15:00
 * @description: EnumValueValidator 的单元测试
 * @since 1.0.0
 */
@DisplayName("EnumValueValidator 测试")
class EnumValueValidatorTest {

    @Mock
    private ConstraintValidatorContext context;

    private EnumValueValidator validator;

    // 测试用枚举
    enum TestEnum {
        VALUE1("code1"), VALUE2("code2"), VALUE3("code3");

        private final String code;

        TestEnum(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        validator = new EnumValueValidator();
    }

    @Test
    @DisplayName("测试使用枚举值列表校验")
    void testValidateWithEnumValues() {
        // 初始化校验器
        EnumValue enumValue = new EnumValue() {
            @Override
            public Class<? extends Enum<?>> value() {
                return TestEnum.class;
            }

            @Override
            public String[] enumValues() {
                return new String[] {"code1", "code2", "code3"};
            }

            @Override
            public String method() {
                return "";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public String message() {
                return "";
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return EnumValue.class;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return new Class[0];
            }
        };

        validator.initialize(enumValue);

        // 测试有效值
        assertTrue(validator.isValid("code1", context));
        assertTrue(validator.isValid("code2", context));
        assertTrue(validator.isValid("code3", context));

        // 测试无效值
        assertFalse(validator.isValid("invalid", context));
        assertFalse(validator.isValid("CODE1", context));

        // 测试 null 值
        assertTrue(validator.isValid(null, context));
    }

    @Test
    @DisplayName("测试使用枚举方法校验")
    void testValidateWithEnumMethod() {
        // 初始化校验器
        EnumValue enumValue = new EnumValue() {
            @Override
            public Class<? extends Enum<?>> value() {
                return TestEnum.class;
            }

            @Override
            public String[] enumValues() {
                return new String[] {};
            }

            @Override
            public String method() {
                return "getCode";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public String message() {
                return "";
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return EnumValue.class;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return new Class[0];
            }
        };

        validator.initialize(enumValue);

        // 测试有效值
        assertTrue(validator.isValid("code1", context));
        assertTrue(validator.isValid("code2", context));
        assertTrue(validator.isValid("code3", context));

        // 测试无效值
        assertFalse(validator.isValid("invalid", context));
        assertFalse(validator.isValid("CODE1", context));

        // 测试 null 值
        assertTrue(validator.isValid(null, context));
    }

    @Test
    @DisplayName("测试使用默认 toString 方法校验")
    void testValidateWithToString() {
        // 初始化校验器
        EnumValue enumValue = new EnumValue() {
            @Override
            public Class<? extends Enum<?>> value() {
                return TestEnum.class;
            }

            @Override
            public String[] enumValues() {
                return new String[] {};
            }

            @Override
            public String method() {
                return "";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public String message() {
                return "";
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return EnumValue.class;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return new Class[0];
            }
        };

        validator.initialize(enumValue);

        // 测试有效值
        assertTrue(validator.isValid("VALUE1", context));
        assertTrue(validator.isValid("VALUE2", context));
        assertTrue(validator.isValid("VALUE3", context));

        // 测试无效值
        assertFalse(validator.isValid("invalid", context));
        assertFalse(validator.isValid("value1", context));

        // 测试 null 值
        assertTrue(validator.isValid(null, context));
    }

    @Test
    @DisplayName("测试空枚举类")
    void testEmptyEnum() {
        // 创建一个空的枚举类
        enum EmptyEnum {
        }

        // 初始化校验器
        EnumValue enumValue = new EnumValue() {
            @Override
            public Class<? extends Enum<?>> value() {
                return EmptyEnum.class;
            }

            @Override
            public String[] enumValues() {
                return new String[] {};
            }

            @Override
            public String method() {
                return "";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public String message() {
                return "";
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return EnumValue.class;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return new Class[0];
            }
        };

        validator.initialize(enumValue);

        // 测试任何值都应该返回 false
        assertFalse(validator.isValid("any", context));
        // null 值仍然应该返回 true
        assertTrue(validator.isValid(null, context));
    }

    @Test
    @DisplayName("测试无效的方法名")
    void testInvalidMethod() {
        // 初始化校验器
        EnumValue enumValue = new EnumValue() {
            @Override
            public Class<? extends Enum<?>> value() {
                return TestEnum.class;
            }

            @Override
            public String[] enumValues() {
                return new String[] {};
            }

            @Override
            public String method() {
                return "invalidMethod";
            }

            @Override
            public Class<?>[] groups() {
                return new Class[0];
            }

            @Override
            public String message() {
                return "";
            }

            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return EnumValue.class;
            }

            @Override
            public Class<? extends Payload>[] payload() {
                return new Class[0];
            }
        };

        validator.initialize(enumValue);

        // 测试当方法不存在时应该返回 false
        assertFalse(validator.isValid("code1", context));
        assertFalse(validator.isValid("code2", context));
        assertFalse(validator.isValid("code3", context));

        // 测试 null 值仍然返回 true
        assertTrue(validator.isValid(null, context));
    }
}