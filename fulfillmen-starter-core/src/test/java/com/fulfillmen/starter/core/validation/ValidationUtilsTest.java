/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.starter.core.validation;

import static org.junit.jupiter.api.Assertions.*;

import com.fulfillmen.starter.core.exception.ClientRequestException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * ValidationUtils 测试类
 *
 * <AUTHOR>
 * @date 2025/02/05 15:00
 * @description: ValidationUtils 的单元测试
 * @since 1.0.0
 */
@DisplayName("ValidationUtils 测试")
class ValidationUtilsTest {

    @Test
    @DisplayName("测试 throwIfNull 方法")
    void testThrowIfNull() {
        // 测试 null 值
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfNull(null, "Object is null"));

        // 测试非 null 值
        assertDoesNotThrow(() -> ValidationUtils.throwIfNull("test", "Object is null"));

        // 测试带参数的消息
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfNull(null, "Field {} is null", "name"));
    }

    @Test
    @DisplayName("测试 throwIfNotNull 方法")
    void testThrowIfNotNull() {
        // 测试 null 值
        assertDoesNotThrow(() -> ValidationUtils.throwIfNotNull(null, "Object is not null"));

        // 测试非 null 值
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfNotNull("test", "Object is not null"));
    }

    @Test
    @DisplayName("测试 throwIfEmpty 方法")
    void testThrowIfEmpty() {
        // 测试 null 值
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfEmpty(null, "Collection is empty"));

        // 测试空集合
        List<String> emptyList = new ArrayList<>();
        assertThrows(ClientRequestException.class, () -> ValidationUtils
            .throwIfEmpty(emptyList, "Collection is empty"));

        // 测试非空集合
        List<String> nonEmptyList = List.of("test");
        assertDoesNotThrow(() -> ValidationUtils.throwIfEmpty(nonEmptyList, "Collection is empty"));

        // 测试空字符串
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfEmpty("", "String is empty"));

        // 测试非空字符串
        assertDoesNotThrow(() -> ValidationUtils.throwIfEmpty("test", "String is empty"));
    }

    @Test
    @DisplayName("测试 throwIfNotEmpty 方法")
    void testThrowIfNotEmpty() {
        // 测试 null 值
        assertDoesNotThrow(() -> ValidationUtils.throwIfNotEmpty(null, "Collection is not empty"));

        // 测试空集合
        List<String> emptyList = new ArrayList<>();
        assertDoesNotThrow(() -> ValidationUtils.throwIfNotEmpty(emptyList, "Collection is not empty"));

        // 测试非空集合
        List<String> nonEmptyList = List.of("test");
        assertThrows(ClientRequestException.class, () -> ValidationUtils
            .throwIfNotEmpty(nonEmptyList, "Collection is not empty"));
    }

    @Test
    @DisplayName("测试 throwIfBlank 方法")
    void testThrowIfBlank() {
        // 测试 null 值
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfBlank(null, "String is blank"));

        // 测试空字符串
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfBlank("", "String is blank"));

        // 测试空白字符串
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfBlank("   ", "String is blank"));

        // 测试非空字符串
        assertDoesNotThrow(() -> ValidationUtils.throwIfBlank("test", "String is blank"));
    }

    @Test
    @DisplayName("测试 throwIfNotBlank 方法")
    void testThrowIfNotBlank() {
        // 测试 null 值
        assertDoesNotThrow(() -> ValidationUtils.throwIfNotBlank(null, "String is not blank"));

        // 测试空字符串
        assertDoesNotThrow(() -> ValidationUtils.throwIfNotBlank("", "String is not blank"));

        // 测试空白字符串
        assertDoesNotThrow(() -> ValidationUtils.throwIfNotBlank("   ", "String is not blank"));

        // 测试非空字符串
        assertThrows(ClientRequestException.class, () -> ValidationUtils
            .throwIfNotBlank("test", "String is not blank"));
    }

    @Test
    @DisplayName("测试 throwIfEqual 方法")
    void testThrowIfEqual() {
        String str1 = "test";
        String str2 = "test";
        String str3 = "other";

        // 测试相等的值
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfEqual(str1, str2, "Objects are equal"));

        // 测试不相等的值
        assertDoesNotThrow(() -> ValidationUtils.throwIfEqual(str1, str3, "Objects are equal"));

        // 测试 null 值
        assertDoesNotThrow(() -> ValidationUtils.throwIfEqual(str1, null, "Objects are equal"));
        assertDoesNotThrow(() -> ValidationUtils.throwIfEqual(null, str1, "Objects are equal"));
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIfEqual(null, null, "Objects are equal"));
    }

    @Test
    @DisplayName("测试 throwIfNotEqual 方法")
    void testThrowIfNotEqual() {
        String str1 = "test";
        String str2 = "test";
        String str3 = "other";

        // 测试相等的值
        assertDoesNotThrow(() -> ValidationUtils.throwIfNotEqual(str1, str2, "Objects are not equal"));

        // 测试不相等的值
        assertThrows(ClientRequestException.class, () -> ValidationUtils
            .throwIfNotEqual(str1, str3, "Objects are not equal"));
    }

    @Test
    @DisplayName("测试 throwIfEqualIgnoreCase 方法")
    void testThrowIfEqualIgnoreCase() {
        String str1 = "test";
        String str2 = "TEST";
        String str3 = "other";

        // 测试大小写不同但相等的值
        assertThrows(ClientRequestException.class, () -> ValidationUtils
            .throwIfEqualIgnoreCase(str1, str2, "Strings are equal ignoring case"));

        // 测试不相等的值
        assertDoesNotThrow(() -> ValidationUtils.throwIfEqualIgnoreCase(str1, str3, "Strings are equal ignoring case"));
    }

    @Test
    @DisplayName("测试 throwIfNotEqualIgnoreCase 方法")
    void testThrowIfNotEqualIgnoreCase() {
        String str1 = "test";
        String str2 = "TEST";
        String str3 = "other";

        // 测试大小写不同但相等的值
        assertDoesNotThrow(() -> ValidationUtils
            .throwIfNotEqualIgnoreCase(str1, str2, "Strings are not equal ignoring case"));

        // 测试不相等的值
        assertThrows(ClientRequestException.class, () -> ValidationUtils
            .throwIfNotEqualIgnoreCase(str1, str3, "Strings are not equal ignoring case"));
    }

    @Test
    @DisplayName("测试 throwIf 方法")
    void testThrowIf() {
        // 测试条件为 true
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIf(true, "Condition is true"));

        // 测试条件为 false
        assertDoesNotThrow(() -> ValidationUtils.throwIf(false, "Condition is true"));

        // 测试带参数的消息
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIf(true, "Value {} is invalid", "test"));
    }

    @Test
    @DisplayName("测试 throwIf 方法 - BooleanSupplier")
    void testThrowIfWithSupplier() {
        // 测试条件为 true
        assertThrows(ClientRequestException.class, () -> ValidationUtils.throwIf(() -> true, "Condition is true"));

        // 测试条件为 false
        assertDoesNotThrow(() -> ValidationUtils.throwIf(() -> false, "Condition is true"));

        // 测试带参数的消息
        assertThrows(ClientRequestException.class, () -> ValidationUtils
            .throwIf(() -> true, "Value {} is invalid", "test"));
    }
}