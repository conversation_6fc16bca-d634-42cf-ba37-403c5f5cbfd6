# 跨线程上下文传递测试说明

## 概述

本测试套件验证了 `ThreadPoolAutoConfiguration` 和 `AsyncAutoConfiguration` 中配置的 TTL（TransmittableThreadLocal）任务装饰器是否能够正确传递上下文信息到异步任务中。

## 测试架构

### 核心组件

1. **ThreadPoolAutoConfiguration**: 配置线程池并设置 TTL 任务装饰器
2. **AsyncAutoConfiguration**: 配置异步执行器
3. **TestContextHolder**: 提供 TTL 变量用于测试
4. **MDC**: 日志诊断上下文，用于测试日志上下文传递

### TTL 配置验证

测试验证了以下 TTL 配置是否生效：

```java
// 在 ThreadPoolAutoConfiguration 中
executor.setTaskDecorator(TtlRunnable::get);
```

## 测试用例结构

### 1. CrossThreadContextTransmissionTest

**主要测试类**，包含以下测试场景：

#### 1.1 MDC日志上下文传递测试

- **测试MDC在异步任务中的传递**: 验证主线程设置的 MDC 信息能否正确传递到异步任务
- **测试MDC在多个并发异步任务中的隔离**: 验证不同线程的 MDC 信息相互独立

#### 1.2 自定义ThreadLocal传递测试

- **测试TestContextHolder中TTL变量的传递**: 验证自定义 TTL 变量的跨线程传递
- **测试TTL变量在异步任务链中的传递**: 验证 TTL 在异步任务链中的传递和修改

#### 1.3 模拟SaToken上下文传递测试

- **测试模拟用户认证信息在异步任务中的传递**: 使用 MDC 模拟 SaToken 认证信息传递
- **测试认证信息在异步任务中的权限验证**: 验证权限信息的正确传递

#### 1.4 参数化测试

- **测试不同线程池配置下的上下文传递**: 验证不同线程池参数下的传递效果

#### 1.5 综合场景测试

- **测试复杂业务场景下的上下文传递**: 模拟真实业务场景
- **测试异常情况下的上下文清理**: 验证异常处理时的上下文状态

### 2. SaTokenContextTransmissionTest

**专门测试 SaToken 上下文传递**，包含：

#### 2.1 用户登录状态传递测试

- **测试用户登录状态在异步任务中的传递**: 验证登录状态传递
- **测试用户权限在异步任务中的验证**: 验证权限检查
- **测试用户角色在异步任务中的验证**: 验证角色检查

#### 2.2 会话信息传递测试

- **测试会话属性在异步任务中的传递**: 验证会话属性传递
- **测试Token信息在异步任务中的传递**: 验证 Token 信息传递

#### 2.3 并发场景测试

- **测试多用户并发场景下的上下文隔离**: 验证多用户并发时的上下文隔离

## 运行测试

### 单独运行测试

```bash
# 运行主要测试类
mvn test -Dtest=CrossThreadContextTransmissionTest

# 运行 SaToken 测试类
mvn test -Dtest=SaTokenContextTransmissionTest

# 运行所有线程池相关测试
mvn test -Dtest="*ThreadPool*Test"
```

### 运行特定测试方法

```bash
# 运行 MDC 传递测试
mvn test -Dtest=CrossThreadContextTransmissionTest#testMDCTransmissionInAsyncTask

# 运行参数化测试
mvn test -Dtest=CrossThreadContextTransmissionTest#testContextTransmissionWithDifferentPoolConfigurations
```

## 测试配置

### 线程池配置

测试使用以下线程池配置：

```properties
spring.task.execution.core-size-ratio=1.0
spring.task.execution.max-size-ratio=2.0
spring.task.execution.queue-capacity=100
spring.task.execution.thread-name-prefix=ttl-test-
```

### 参数化测试配置

参数化测试验证以下配置组合：

| 核心线程比例 | 最大线程比例 | 队列容量 | 场景描述   |
|--------|--------|------|--------|
| 0.5    | 1.0    | 50   | 小线程池配置 |
| 1.0    | 2.0    | 100  | 默认配置   |
| 2.0    | 4.0    | 200  | 大线程池配置 |

## 验证要点

### 1. TTL 传递验证

✅ **MDC 信息传递**: 验证 userId、tenantId、traceId 等 MDC 信息能正确传递到异步线程

✅ **自定义 TTL 变量传递**: 验证 TestContextHolder 中的 TTL 变量能正确传递

✅ **上下文隔离**: 验证不同线程的上下文信息相互独立

### 2. 异步任务链传递验证

✅ **任务链传递**: 验证 TTL 在 `CompletableFuture` 任务链中的传递

✅ **上下文修改**: 验证在异步任务中修改 TTL 变量的效果

### 3. 异常处理验证

✅ **异常情况下的上下文状态**: 验证异步任务抛出异常时，主线程上下文不受影响

✅ **上下文清理**: 验证异步任务结束后的上下文清理

### 4. 性能和并发验证

✅ **并发执行**: 验证多个并发异步任务的上下文传递

✅ **线程池配置影响**: 验证不同线程池配置对上下文传递的影响

## 模拟 SaToken 实现

由于测试环境中没有完整的 SaToken 配置，使用 `MockSaTokenContext` 模拟 SaToken 的行为：

```java
// 模拟登录
MockSaTokenContext.login("user-001","张三","admin");

// 模拟权限设置
MockSaTokenContext.

setPermissions("user:read","user:write");

// 模拟角色设置
MockSaTokenContext.

setRoles("admin","user");

// 模拟会话属性
MockSaTokenContext.

setSessionAttribute("department","技术部");
```

在实际项目中，这些调用应该替换为真实的 SaToken API：

```java
// 真实的 SaToken API
StpUtil.login("user-001");
StpUtil.

checkPermission("user:read");
StpUtil.

checkRole("admin");
StpUtil.

getSession().

set("department","技术部");
```

## 测试结果示例

### 成功的测试输出

```
[ttl-test-1] INFO  - 异步任务获取MDC信息: {userId=12345, tenantId=tenant-001, traceId=trace-1704268800000, threadName=ttl-test-1}
[ttl-test-2] INFO  - 异步任务获取TestContext信息: {testValue=test-value-1704268800000, testContext={id=ctx-001, name=测试上下文}, threadName=ttl-test-2}
[ttl-test-3] INFO  - 异步任务获取用户信息: {isLogin=true, loginId=test-user-001, userName=张三, userRole=admin, threadName=ttl-test-3}
```

### 验证的关键点

1. **线程名称**: 确认异步任务在配置的线程池中执行
2. **上下文信息**: 确认 MDC 和 TTL 信息正确传递
3. **数据一致性**: 确认传递的数据与主线程设置的一致
4. **并发隔离**: 确认不同任务的上下文相互独立

## 故障排除

### 常见问题

1. **TTL 不生效**: 检查 `executor.setTaskDecorator(TtlRunnable::get)` 是否正确配置
2. **MDC 信息丢失**: 检查 MDC 是否在主线程正确设置
3. **测试超时**: 检查线程池配置和任务执行时间
4. **上下文污染**: 确保测试前后正确清理上下文

### 调试建议

1. **启用调试日志**: 设置日志级别为 DEBUG 查看详细信息
2. **检查线程名称**: 确认异步任务在正确的线程池中执行
3. **验证配置加载**: 确认线程池配置正确加载
4. **单步调试**: 在关键点设置断点查看上下文状态

## 扩展测试

### 添加新的测试场景

1. **自定义 TTL 变量**: 添加新的 TTL 变量测试
2. **复杂对象传递**: 测试复杂对象的跨线程传递
3. **性能测试**: 测试 TTL 对性能的影响
4. **错误场景**: 测试各种异常情况

### 集成真实 SaToken

在实际项目中集成真实的 SaToken 测试：

```java

@SpringBootTest
@EnableSaToken
class RealSaTokenContextTransmissionTest {

    @Test
    void testRealSaTokenTransmission() {
        // 使用真实的 SaToken API
        StpUtil.login("user-001");

        // 执行异步任务
        CompletableFuture<Boolean> future = asyncService.checkLogin();

        // 验证结果
        assertTrue(future.get());
    }
}
```

## 总结

这个测试套件全面验证了 TTL 任务装饰器的配置和功能，确保：

1. ✅ MDC 日志上下文能正确传递到异步任务
2. ✅ 自定义 TTL 变量能正确传递到异步任务
3. ✅ 模拟的 SaToken 上下文能正确传递到异步任务
4. ✅ 不同线程池配置下的上下文传递都能正常工作
5. ✅ 并发场景下的上下文隔离正确
6. ✅ 异常情况下的上下文状态正确

通过这些测试，可以确信 `ThreadPoolAutoConfiguration` 和 `AsyncAutoConfiguration` 中的 TTL 配置能够满足实际项目中的跨线程上下文传递需求。 