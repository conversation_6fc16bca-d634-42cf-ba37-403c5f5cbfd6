import api from '@/api/config/axios'
import type { AlibabaCategoryVO, ApiResponse } from '@/api/types'
import { getCache, setCache } from '@/utils/cache'
import Dexie, { Table } from 'dexie'

// 数据库配置
const DB_NAME = 'fulfillmen-shop'
const DB_VERSION = 1
const CATEGORIES_STORE = 'categories'
const CACHE_KEY = 'categories_cache'
const CACHE_EXPIRE = 60 * 60 * 1000 // 60分钟

// 定义缓存项类型（不直接扩展AlibabaCategoryVO避免类型冲突）
interface CategoryCacheItem {
    id?: number
    categoryId: string | number
    parentId?: number
    level?: number
    chineseName?: string
    translatedName?: string
    status?: number
    sort?: number
    childCategories?: AlibabaCategoryVO[]
    // 缓存特有字段
    data?: ApiResponse<AlibabaCategoryVO[]>
    cacheExpire?: number
    createdAt?: number
}

/**
 * 分类数据管理类
 * 基于 Dexie 实现对分类数据的存储、查询、搜索等功能
 */
class CategoryDB extends Dexie {
    // 定义表类型
    categories!: Table<CategoryCacheItem, number>

    constructor() {
        super(DB_NAME)

        // 定义数据库结构
        this.version(DB_VERSION).stores({
            [CATEGORIES_STORE]: '++id, categoryId, parentId, level, createdAt',
        })
    }

    /**
     * 获取分类数据（优先从缓存获取，过期则请求API）
     * @returns 分类数据列表
     */
    async getCategories(): Promise<ApiResponse<AlibabaCategoryVO[]>> {
        try {
            // 尝试从数据库获取缓存
            const cachedItem = await this.categories.get({ categoryId: CACHE_KEY })

            if (cachedItem && cachedItem.cacheExpire && cachedItem.cacheExpire > Date.now()) {
                console.log('从Dexie缓存获取分类数据')
                return cachedItem.data as ApiResponse<AlibabaCategoryVO[]>
            }

            // Dexie缓存未命中，尝试从localStorage读取（兼容旧版本）
            const legacyCached = getCache<ApiResponse<AlibabaCategoryVO[]>>(CACHE_KEY)
            if (legacyCached) {
                console.log('从localStorage缓存获取分类数据')
                // 更新到Dexie
                await this.saveCategories(legacyCached)
                return legacyCached
            }

            // 请求API
            console.log('缓存未命中，请求分类API')
            const response = await api.get<ApiResponse<AlibabaCategoryVO[]>>('/api/home/<USER>')

            // 更新缓存
            await this.saveCategories(response.data)
            setCache(CACHE_KEY, response.data, CACHE_EXPIRE) // 同时更新localStorage作为备份

            return response.data
        } catch (error) {
            console.error('获取分类失败:', error)

            // 错误处理，尝试从localStorage获取
            const fallbackCache = getCache<ApiResponse<AlibabaCategoryVO[]>>(CACHE_KEY)
            if (fallbackCache) {
                console.log('发生错误，使用localStorage缓存数据')
                return fallbackCache
            }

            throw error
        }
    }

    /**
     * 保存分类数据到Dexie数据库
     * @param data 分类数据
     */
    private async saveCategories(data: ApiResponse<AlibabaCategoryVO[]>): Promise<void> {
        try {
            // 清除旧数据
            await this.categories.clear()

            // 添加缓存标记
            const cacheItem: CategoryCacheItem = {
                categoryId: CACHE_KEY,
                data: data,
                cacheExpire: Date.now() + CACHE_EXPIRE,
                createdAt: Date.now(),
            }

            // 保存到数据库
            await this.categories.add(cacheItem)

            // 如果数据包含分类列表，分别保存各个分类
            if (data.data && Array.isArray(data.data)) {
                const categories = data.data as AlibabaCategoryVO[]
                // 批量添加所有分类项
                const categoryItems = categories.map(category => {
                    const item: CategoryCacheItem = {
                        categoryId: category.categoryId,
                        parentId: category.parentId,
                        level: category.level,
                        chineseName: category.chineseName,
                        translatedName: category.translatedName,
                        status: category.status,
                        sort: category.sort,
                        childCategories: category.childCategories,
                        createdAt: Date.now(),
                    }
                    return item
                })

                await this.categories.bulkAdd(categoryItems)
            }
        } catch (error) {
            console.error('保存分类数据失败:', error)
        }
    }

    /**
     * 搜索分类数据
     * @param query 搜索关键词
     * @returns 匹配的分类列表
     */
    async searchCategories(query: string): Promise<AlibabaCategoryVO[]> {
        try {
            // 确保有数据
            await this.getCategories()

            // 从数据库中搜索
            const lowerQuery = query.toLowerCase()
            const results = await this.categories
                .filter((category: CategoryCacheItem): boolean => {
                    // 排除缓存项
                    if (typeof category.categoryId === 'string' && category.categoryId === CACHE_KEY) {
                        return false
                    }

                    // 搜索匹配条件
                    const matchesChineseName = !!category.chineseName && category.chineseName.toLowerCase().includes(lowerQuery)
                    const matchesTranslatedName = !!category.translatedName && category.translatedName.toLowerCase().includes(lowerQuery)

                    return matchesChineseName || matchesTranslatedName
                })
                .toArray()

            // 转换回AlibabaCategoryVO类型
            return results.map(item => ({
                id: typeof item.id === 'number' ? item.id : 0,
                categoryId: typeof item.categoryId === 'number' ? item.categoryId : Number(item.categoryId),
                chineseName: item.chineseName || '',
                translatedName: item.translatedName || '',
                parentId: item.parentId || 0,
                level: item.level || 0,
                status: item.status || 0,
                sort: item.sort || 0,
                childCategories: item.childCategories || [],
            }))
        } catch (error) {
            console.error('搜索分类失败:', error)
            return []
        }
    }

    /**
     * 获取指定分类的子分类
     * @param parentId 父分类ID
     * @returns 子分类列表
     */
    async getSubCategories(parentId: number | string): Promise<AlibabaCategoryVO[]> {
        try {
            // 确保有数据
            await this.getCategories()

            const parentIdNumber = typeof parentId === 'string' ? Number(parentId) : parentId

            // 查询指定父ID的分类
            const results = await this.categories
                .filter((category: CategoryCacheItem): boolean => {
                    // 排除缓存项
                    if (typeof category.categoryId === 'string' && category.categoryId === CACHE_KEY) {
                        return false
                    }

                    return category.parentId === parentIdNumber
                })
                .toArray()

            // 转换回AlibabaCategoryVO类型
            return results.map(item => ({
                id: typeof item.id === 'number' ? item.id : 0,
                categoryId: typeof item.categoryId === 'number' ? item.categoryId : Number(item.categoryId),
                chineseName: item.chineseName || '',
                translatedName: item.translatedName || '',
                parentId: item.parentId || 0,
                level: item.level || 0,
                status: item.status || 0,
                sort: item.sort || 0,
                childCategories: item.childCategories || [],
            }))
        } catch (error) {
            console.error('获取子分类失败:', error)
            return []
        }
    }

    /**
     * 清除分类缓存
     */
    async clearCache(): Promise<void> {
        try {
            await this.categories.clear()
            setCache(CACHE_KEY, null, 0) // 同时清除localStorage缓存
            console.log('分类缓存已清除')
        } catch (error) {
            console.error('清除分类缓存失败:', error)
        }
    }
}

// 导出单例实例
export const categoryDB = new CategoryDB()
