<template>
    <div class="flex items-center justify-between pb-4 border-b border-gray-100">
        <div class="flex items-center text-gray-500 text-sm">
            {{ $t('search.product.orderBy') }}
            <select v-model="localSortBy" class="ml-2 h-9 rounded-md border border-gray-300 py-1.5 pl-3 pr-10 text-gray-900 focus:ring-1 focus:ring-indigo-500 sm:text-sm">
                <option v-for="option in sortOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                </option>
            </select>
        </div>
        <div class="flex items-center">
            <button
                v-if="!isMobileView"
                type="button"
                @click="setGridColumns(5)"
                class="p-2 flex items-center transition-colors"
                :class="localGridColumns === 5 ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-500'"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
            </button>
            <button
                v-if="!isMobileView"
                type="button"
                @click="setGridColumns(3)"
                class="p-2 flex items-center transition-colors"
                :class="localGridColumns === 3 ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-500'"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path
                        d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                    />
                </svg>
            </button>
            <button
                type="button"
                @click="setGridColumns(isMobileView ? 2 : 1)"
                class="p-2 flex items-center transition-colors"
                :class="localGridColumns === (isMobileView ? 2 : 1) ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-500'"
            >
                <svg v-if="isMobileView" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path
                        d="M2 3a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm0 8a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1H3a1 1 0 01-1-1v-4zM10 3a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1V3zm0 8a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"
                    />
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path
                        fill-rule="evenodd"
                        d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                        clip-rule="evenodd"
                    />
                </svg>
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, onUnmounted, ref, watch } from 'vue'
    import { useI18n } from 'vue-i18n'

    const { t } = useI18n()

    const props = defineProps<{
        sortBy: string
        gridColumns: number
    }>()

    const emit = defineEmits<{
        (e: 'update:sortBy', value: string): void
        (e: 'update:gridColumns', value: number): void
    }>()

    const localSortBy = ref(props.sortBy)
    const localGridColumns = ref(props.gridColumns)

    // Responsive visibility for column buttons
    const isMobileView = ref(window.innerWidth < 768) // md breakpoint

    const updateView = () => {
        isMobileView.value = window.innerWidth < 768

        // 自动调整列数，在移动设备上最多显示2列
        if (isMobileView.value && localGridColumns.value > 2) {
            setGridColumns(2)
        }
    }

    onMounted(() => {
        window.addEventListener('resize', updateView)
        updateView() // Initial check
    })

    onUnmounted(() => {
        window.removeEventListener('resize', updateView)
    })

    // 排序选项
    const sortOptions = [
        { value: 'popular', label: t('search.mostPopular') },
        { value: 'sales-desc', label: t('search.bestsellers') }, // 对应 monthSold desc
        { value: 'rating-desc', label: t('search.bestRating') }, // 对应 rePurchaseRate desc
        { value: 'price-asc', label: t('search.priceLowToHigh') },
        { value: 'price-desc', label: t('search.priceHighToLow') },
    ]

    // 监听本地值变化，通知父组件
    watch(localSortBy, newValue => {
        emit('update:sortBy', newValue)
    })

    watch(
        () => props.sortBy,
        newValue => {
            localSortBy.value = newValue
        }
    )

    watch(
        () => props.gridColumns,
        newValue => {
            // 确保在移动设备上不超过2列
            if (isMobileView.value && newValue > 2) {
                localGridColumns.value = 2
                emit('update:gridColumns', 2)
            } else {
                localGridColumns.value = newValue
            }
        }
    )

    // 设置网格列数
    const setGridColumns = (columns: number) => {
        // 确保在移动设备上不超过2列
        if (isMobileView.value && columns > 2) {
            columns = 2
        }
        localGridColumns.value = columns
        emit('update:gridColumns', columns)
    }
</script>
