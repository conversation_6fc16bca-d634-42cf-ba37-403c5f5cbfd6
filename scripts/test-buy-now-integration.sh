#!/bin/bash

# 测试立即购买功能集成脚本
# 验证前端到后端的完整流程

echo "========================================="
echo "测试立即购买功能集成"
echo "========================================="

# 设置测试参数
API_HOST="http://localhost:8080"
PRODUCT_ID="************"  # 测试商品ID
SKU_ID="3332085412530"     # 测试SKU ID

echo ""
echo "1. 测试订单预览接口 - /api/order/preview"
echo "----------------------------------------"

# 构建正确的请求参数
ORDER_PREVIEW_DATA='{
    "productList": [
        {
            "skuId": 3332085412530,
            "productQuantity": 2
        }
    ]
}'

echo "请求数据:"
echo "$ORDER_PREVIEW_DATA" | jq .

echo ""
echo "发送请求到: $API_HOST/api/order/preview"

# 发送订单预览请求
PREVIEW_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$ORDER_PREVIEW_DATA" \
    "$API_HOST/api/order/preview")

echo ""
echo "响应结果:"
echo "$PREVIEW_RESPONSE" | jq .

# 检查响应是否包含错误
if echo "$PREVIEW_RESPONSE" | jq -e '.error' > /dev/null 2>&1; then
    echo ""
    echo "❌ 订单预览失败!"
    echo "错误信息:"
    echo "$PREVIEW_RESPONSE" | jq -r '.error // .message // "未知错误"'
    exit 1
fi

# 检查是否成功返回预览数据
if echo "$PREVIEW_RESPONSE" | jq -e '.orderPreviewSummary' > /dev/null 2>&1; then
    echo ""
    echo "✅ 订单预览成功!"

    # 显示预览摘要
    echo ""
    echo "预览摘要:"
    echo "商品总数量: $(echo "$PREVIEW_RESPONSE" | jq -r '.orderPreviewSummary.totalQuantity // "N/A"')"
    echo "商品种类数: $(echo "$PREVIEW_RESPONSE" | jq -r '.orderPreviewSummary.productTypeCount // "N/A"')"
    echo "预览状态: $(echo "$PREVIEW_RESPONSE" | jq -r '.orderPreviewSummary.success // "N/A"')"
    echo "是否有错误: $(echo "$PREVIEW_RESPONSE" | jq -r '.orderPreviewSummary.hasErrors // "N/A"')"

    # 显示价格信息
    echo ""
    echo "价格详情:"
    echo "商品金额: ¥$(echo "$PREVIEW_RESPONSE" | jq -r '.priceDetails.merchandiseAmount // "N/A"')"
    echo "运费: ¥$(echo "$PREVIEW_RESPONSE" | jq -r '.priceDetails.shippingAmount // "N/A"')"
    echo "服务费: ¥$(echo "$PREVIEW_RESPONSE" | jq -r '.priceDetails.serviceFee // "N/A"')"
    echo "总金额: ¥$(echo "$PREVIEW_RESPONSE" | jq -r '.priceDetails.totalAmount // "N/A"')"

    # 显示商品信息
    echo ""
    echo "商品列表:"
    echo "$PREVIEW_RESPONSE" | jq -r '.tradeProductItems[] | "- \(.productTitle // "未知商品") x\(.orderedQuantity // 0) = ¥\(.lineTotalAmount // 0)"'
else
    echo ""
    echo "❌ 订单预览响应格式异常!"
    echo "响应内容:"
    echo "$PREVIEW_RESPONSE"
    exit 1
fi

echo ""
echo "========================================="
echo "2. 测试前端立即购买流程"
echo "========================================="

echo ""
echo "前端测试步骤："
echo "1. 打开浏览器访问: http://localhost:5173"
echo "2. 进入商品详情页: http://localhost:5173/products/$PRODUCT_ID"
echo "3. 选择商品规格和数量"
echo "4. 点击'立即购买'按钮"
echo "5. 检查是否正确跳转到结账页面"
echo "6. 验证订单预览数据是否正确显示"

echo ""
echo "预期行为："
echo "- 点击立即购买后应该调用 /api/order/preview 接口"
echo "- 成功获取预览数据后跳转到 /checkout?previewId=xxx"
echo "- 结账页面应该显示预览的商品和价格信息"
echo "- 页面顶部应该显示'订单预览模式'提示"

echo ""
echo "========================================="
echo "3. 常见问题排查"
echo "========================================="

echo ""
echo "如果遇到问题，请检查："
echo "1. 后端服务是否启动 (端口8080)"
echo "2. 前端服务是否启动 (端口5173)"
echo "3. 数据库中是否存在测试商品数据"
echo "4. SKU ID是否正确"
echo "5. 浏览器控制台是否有错误信息"

echo ""
echo "调试命令："
echo "- 查看后端日志: tail -f fulfillmen-shop-bootstrap/logs/application.log"
echo "- 查看数据库商品: SELECT * FROM tz_product_sku WHERE sku_id = $SKU_ID;"
echo "- 测试商品详情接口: curl $API_HOST/api/product/detail/$PRODUCT_ID"

echo ""
echo "========================================="
echo "测试完成！"
echo "========================================="
